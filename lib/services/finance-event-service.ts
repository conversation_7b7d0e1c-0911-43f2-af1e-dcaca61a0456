'use client';

import { getAllCashTransactions, getAllOrders } from '@/lib/db/v4';
import { FinanceEvent } from '@/app/components/finance/EventBasedFinanceHistory';

/**
 * Service to convert current transaction data to event-based finance history
 */

export async function getFinanceEvents(): Promise<FinanceEvent[]> {
  try {
    const [cashTransactions, orders] = await Promise.all([
      getAllCashTransactions(),
      getAllOrders()
    ]);

    const events: FinanceEvent[] = [];

    // Process cash transactions (these create actual money movements)
    for (const tx of cashTransactions) {
      const event = await convertTransactionToEvent(tx, orders);
      if (event) {
        events.push(event);
      }
    }

    // Add all completed delivery orders (even if not collected yet)
    // This shows the full order amount regardless of collection status
    const completedDeliveryOrders = orders.filter(order =>
      order.orderType === 'delivery' &&
      order.status === 'completed' &&
      order.paymentStatus === 'paid' &&
      order.collectionStatus // Has collection status (collection-based)
    );

    for (const order of completedDeliveryOrders) {
      // Check if we already have a cash transaction for this order
      const hasTransaction = cashTransactions.some(tx => tx.relatedDocId === order._id);

      if (!hasTransaction) {
        // This is a delivery order that hasn't been collected yet
        events.push({
          id: `order_${order._id}`,
          type: 'order_payment',
          timestamp: order.completedAt || order.updatedAt,
          netAmount: order.total, // Show full order amount
          performedBy: 'System',
          description: `Commande livraison - ${order.customer?.name || 'Client'}`,
          orderPayment: {
            orderId: order._id,
            orderType: 'delivery',
            customer: order.customer?.name || order.customer?.phone || 'Client',
            items: order.items?.map((item: any) => item.name).join(', ') || 'Articles',
            paymentMethod: 'cash',
            collectionStatus: order.collectionStatus?.isPending ? 'not_collected' : 'collected'
          }
        });
      }
    }

    // Sort by timestamp, most recent first
    return events.sort((a, b) =>
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    );
  } catch (error) {
    console.error('[getFinanceEvents] Error:', error);
    return [];
  }
}

async function convertTransactionToEvent(
  transaction: any, 
  orders: any[]
): Promise<FinanceEvent | null> {
  const transactionType = transaction.transactionType || transaction.type;
  
  try {
    switch (transactionType) {
      case 'sales':
        return await convertSalesTransaction(transaction, orders);
      
      case 'manual_in':
        return convertManualInTransaction(transaction);
      
      case 'manual_out':
        return convertManualOutTransaction(transaction);
      
      case 'expense':
        return convertExpenseTransaction(transaction);
      
      default:
        console.warn('[convertTransactionToEvent] Unknown transaction type:', transactionType);
        return null;
    }
  } catch (error) {
    console.error('[convertTransactionToEvent] Error converting transaction:', error);
    return null;
  }
}

async function convertSalesTransaction(
  transaction: any,
  orders: any[]
): Promise<FinanceEvent | null> {
  const metadata = transaction.metadata || {};

  // Check if this is a delivery collection event
  if (metadata.transactionCategory === 'delivery_collection') {
    return convertDeliveryCollection(transaction, orders);
  }

  // Regular order payment - find the related order
  const relatedOrder = orders.find(order => order._id === transaction.relatedDocId);

  if (!relatedOrder) {
    // Generic sales transaction without order details
    return {
      id: transaction._id,
      type: 'order_payment',
      timestamp: transaction.time,
      netAmount: transaction.amount,
      performedBy: transaction.performedBy,
      description: transaction.description,
      orderPayment: {
        orderId: transaction.relatedDocId || 'Unknown',
        orderType: 'dine-in',
        customer: 'Client',
        items: 'Articles divers',
        paymentMethod: 'cash',
        collectionStatus: 'immediate' // Paid immediately
      }
    };
  }

  // Determine collection status for delivery orders
  let collectionStatus = 'immediate'; // Default for dine-in/takeaway
  if (relatedOrder.orderType === 'delivery') {
    if (relatedOrder.collectionStatus?.isPending === false) {
      collectionStatus = 'collected';
    } else if (relatedOrder.collectionStatus?.isPending === true) {
      collectionStatus = 'not_collected';
    } else {
      collectionStatus = 'immediate'; // Prepaid delivery
    }
  }

  return {
    id: transaction._id,
    type: 'order_payment',
    timestamp: transaction.time,
    netAmount: transaction.amount,
    performedBy: transaction.performedBy,
    description: transaction.description,
    orderPayment: {
      orderId: relatedOrder._id,
      orderType: relatedOrder.orderType || 'dine-in',
      customer: relatedOrder.customer?.name || relatedOrder.customer?.phone || 'Client',
      tableId: relatedOrder.tableId,
      items: relatedOrder.items?.map((item: any) => item.name).join(', ') || 'Articles',
      paymentMethod: relatedOrder.paymentMethod || 'cash',
      collectionStatus
    }
  };
}

function convertDeliveryCollection(
  transaction: any,
  orders: any[]
): FinanceEvent {
  const metadata = transaction.metadata || {};

  // Get collection details from metadata
  const driverName = metadata.staffName || metadata.driverName || 'Livreur';
  const orderIds = metadata.orderIds || [];
  const orderCount = metadata.orderCount || orderIds.length || 1;

  // Find related orders
  const relatedOrders = orders.filter(order =>
    orderIds.includes(order._id)
  );

  // Calculate collection details
  const collectionOrders = relatedOrders.map(order => ({
    orderId: order._id,
    amount: order.total || 0,
    customer: order.customer?.name || order.customer?.phone || 'Client'
  }));

  const grossAmount = metadata.expectedAmount || metadata.actualAmount || 0;
  const driverFee = metadata.driverFee || 0;
  const netAmount = transaction.amount; // Net amount that entered the drawer

  return {
    id: transaction._id,
    type: 'collection_completed',
    timestamp: transaction.time,
    netAmount: netAmount, // This is the net amount that actually entered the drawer
    performedBy: transaction.performedBy,
    description: `Collection - ${driverName} (${orderCount} commande${orderCount > 1 ? 's' : ''})`,
    collection: {
      driverName,
      driverId: metadata.staffId || metadata.driverId || 'unknown',
      driverType: metadata.driverType || 'staff',
      orderCount,
      orders: collectionOrders,
      grossAmount,
      driverFee,
      netAmount,
      status: 'completed',
      expectedAmount: metadata.expectedAmount,
      actualAmount: metadata.actualAmount,
      discrepancy: metadata.discrepancy || 0
    }
  };
}

function convertManualInTransaction(transaction: any): FinanceEvent {
  return {
    id: transaction._id,
    type: 'manual_in',
    timestamp: transaction.time,
    netAmount: transaction.amount,
    performedBy: transaction.performedBy,
    description: transaction.description,
    manualTransaction: {
      reason: transaction.description,
      category: 'Dépôt',
      notes: transaction.metadata?.notes
    }
  };
}

function convertManualOutTransaction(transaction: any): FinanceEvent {
  return {
    id: transaction._id,
    type: 'manual_out',
    timestamp: transaction.time,
    netAmount: transaction.amount, // Already negative
    performedBy: transaction.performedBy,
    description: transaction.description,
    manualTransaction: {
      reason: transaction.description,
      category: 'Retrait',
      notes: transaction.metadata?.notes
    }
  };
}

function convertExpenseTransaction(transaction: any): FinanceEvent {
  return {
    id: transaction._id,
    type: 'expense',
    timestamp: transaction.time,
    netAmount: transaction.amount, // Already negative
    performedBy: transaction.performedBy,
    description: transaction.description,
    manualTransaction: {
      reason: transaction.description,
      category: 'Dépense',
      notes: transaction.metadata?.notes
    }
  };
}

/**
 * Get pending collections that haven't been physically collected yet
 * Based on orders with collectionStatus.isPending = true
 */
export async function getPendingCollections(): Promise<FinanceEvent[]> {
  try {
    const orders = await getAllOrders();

    // Find delivery orders that are completed but collection is still pending
    const pendingCollectionOrders = orders.filter(order =>
      order.orderType === 'delivery' &&
      order.status === 'completed' &&
      order.deliveryStatus === 'delivered' &&
      order.collectionStatus?.isPending === true
    );

    console.log(`[getPendingCollections] Found ${pendingCollectionOrders.length} pending collection orders`);

    // Group by driver
    const driverGroups = new Map<string, any[]>();

    pendingCollectionOrders.forEach(order => {
      const driverId = order.deliveryPerson?.id || order.deliveryPerson?.name || 'unknown';
      if (!driverGroups.has(driverId)) {
        driverGroups.set(driverId, []);
      }
      driverGroups.get(driverId)!.push(order);
    });

    // Create pending collection events
    const pendingEvents: FinanceEvent[] = [];

    driverGroups.forEach((orders, driverId) => {
      const firstOrder = orders[0];
      const driverName = firstOrder.deliveryPerson?.name || 'Livreur';
      const driverType = firstOrder.deliveryPerson?.type || 'staff';

      // Calculate amounts
      const grossAmount = orders.reduce((sum, order) => sum + (order.collectionStatus?.expectedAmount || order.total || 0), 0);
      const driverFee = orders.reduce((sum, order) => sum + (order.deliveryTariff || 0), 0);
      const netAmount = grossAmount - driverFee;

      const collectionOrders = orders.map(order => ({
        orderId: order._id,
        amount: order.collectionStatus?.expectedAmount || order.total || 0,
        customer: order.customer?.name || order.customer?.phone || 'Client'
      }));

      // Use the most recent completion time as the event timestamp
      const latestCompletionTime = orders
        .map(order => order.completedAt || order.updatedAt)
        .filter(Boolean)
        .sort()
        .pop() || new Date().toISOString();

      pendingEvents.push({
        id: `pending_collection_${driverId}_${Date.now()}`,
        type: 'collection_pending',
        timestamp: latestCompletionTime,
        netAmount: 0, // No money in drawer yet - pending
        performedBy: 'System',
        description: `Collection en attente - ${driverName} (${orders.length} commande${orders.length > 1 ? 's' : ''})`,
        collection: {
          driverName,
          driverId,
          driverType,
          orderCount: orders.length,
          orders: collectionOrders,
          grossAmount,
          driverFee,
          netAmount,
          status: 'pending',
          expectedAmount: grossAmount
        }
      });
    });

    console.log(`[getPendingCollections] Created ${pendingEvents.length} pending collection events`);
    return pendingEvents;
  } catch (error) {
    console.error('[getPendingCollections] Error:', error);
    return [];
  }
}

/**
 * Get all finance events including both completed transactions and pending collections
 */
export async function getAllFinanceEvents(includePending: boolean = true): Promise<FinanceEvent[]> {
  try {
    const [completedEvents, pendingEvents] = await Promise.all([
      getFinanceEvents(),
      includePending ? getPendingCollections() : Promise.resolve([])
    ]);

    const allEvents = [...completedEvents, ...pendingEvents];
    
    // Sort by timestamp, most recent first
    return allEvents.sort((a, b) => 
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    );
  } catch (error) {
    console.error('[getAllFinanceEvents] Error:', error);
    return [];
  }
}

/**
 * Hook to use finance events in React components
 */
export function useFinanceEvents(includePending: boolean = true) {
  const [events, setEvents] = React.useState<FinanceEvent[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);

  const refreshEvents = React.useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const financeEvents = await getAllFinanceEvents(includePending);
      setEvents(financeEvents);
    } catch (err) {
      console.error('[useFinanceEvents] Error:', err);
      setError('Failed to load finance events');
    } finally {
      setLoading(false);
    }
  }, [includePending]);

  React.useEffect(() => {
    refreshEvents();
  }, [refreshEvents]);

  return {
    events,
    loading,
    error,
    refreshEvents
  };
}

// Add React import for the hook
import React from 'react';
