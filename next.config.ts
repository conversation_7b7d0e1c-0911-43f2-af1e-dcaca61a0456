import type { NextConfig } from "next";
import path from 'path';

const nextConfig: NextConfig = {
  // Disable sourcemaps in production to prevent source code leakage
  productionBrowserSourceMaps: false,
  
  // 🔧 CRITICAL: Configure static export for electron, static, and mobile builds
  ...(process.env.BUILD_TARGET === 'electron' || process.env.BUILD_TARGET === 'static' || process.env.BUILD_TARGET === 'mobile' ? {
    output: 'export',
    trailingSlash: true,
    images: {
      unoptimized: true,
      loader: 'custom',
      loaderFile: './lib/utils/image-loader.js'
    },
    // 🚫 API routes are automatically excluded from static export in App Router
  } : {
    // For non-static builds, keep default image optimization
    images: {
      domains: ['res.cloudinary.com'],
    },
  }),

  experimental: {
    // Increase header size limit for large data operations
    largePageDataBytes: 128 * 1000, // 128KB
  },
  
  // Disable React strict mode to reduce hydration issues during development
  reactStrictMode: false,
  
  // 🎯 Enhanced webpack configuration for different build targets
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // 🔧 GLOBAL FIX: Add comprehensive polyfills for static builds
    if (process.env.BUILD_TARGET === 'electron' || process.env.BUILD_TARGET === 'static' || process.env.BUILD_TARGET === 'mobile') {
      // Add global polyfills at the very beginning
      config.plugins.unshift(
        new webpack.DefinePlugin({
          'self': 'typeof self !== "undefined" ? self : typeof window !== "undefined" ? window : typeof global !== "undefined" ? global : {}',
          'global': 'typeof global !== "undefined" ? global : typeof window !== "undefined" ? window : typeof self !== "undefined" ? self : {}',
          'window': 'typeof window !== "undefined" ? window : typeof global !== "undefined" ? global : typeof self !== "undefined" ? self : {}',
        })
      );

      // Fix webpack runtime globals for server-side rendering
      if (isServer) {
        config.plugins.push(
          new webpack.BannerPlugin({
            banner: `
              if (typeof self === "undefined") { global.self = global; }
              if (typeof window === "undefined") { global.window = global; }
              if (typeof Array === "undefined") { global.Array = Array; }
            `,
            raw: true,
            entryOnly: false,
          })
        );
      }

      // Fix client-side polyfills for Electron
      if (!isServer) {
        config.plugins.push(
          new webpack.BannerPlugin({
            banner: `
              // Electron/Static build polyfills
              if (typeof Array.prototype.forEach === "undefined") {
                Array.prototype.forEach = function(callback, thisArg) {
                  for (var i = 0; i < this.length; i++) {
                    callback.call(thisArg, this[i], i, this);
                  }
                };
              }
              // Ensure Array.isArray exists
              if (typeof Array.isArray === "undefined") {
                Array.isArray = function(obj) {
                  return Object.prototype.toString.call(obj) === '[object Array]';
                };
              }
            `,
            raw: true,
            entryOnly: false,
          })
        );
      }
    }

    // 🔧 ELECTRON FIX: Enhanced configuration for static builds
    if (process.env.BUILD_TARGET === 'electron' || process.env.BUILD_TARGET === 'static') {
      // Set relative public path for static builds
      config.output = {
        ...config.output,
        publicPath: './_next/',
      };

      // Additional webpack optimizations for static builds
      config.optimization = {
        ...config.optimization,
        // Ensure consistent chunk naming for static builds
        chunkIds: 'deterministic',
        moduleIds: 'deterministic',
      };
    }
    // Always handle Capacitor HTTP for all builds except actual mobile deployment
    config.resolve.alias = {
      ...config.resolve.alias,
      '@capacitor/http': path.resolve(__dirname, 'lib/services/empty-capacitor-http.js'),
    };

    // 🌐 Web builds: Exclude restaurant functionality (landing page only)
    if (process.env.BUILD_TARGET === 'web') {
      config.resolve.alias = {
        ...config.resolve.alias,
        // Replace problematic services with empty modules for web builds
        '@/lib/services/kitchen-print-service': path.resolve(__dirname, 'lib/services/empty-service.js'),
        '@/lib/services/barcode-service': path.resolve(__dirname, 'lib/services/empty-service.js'),
        '@/lib/services/print-service': path.resolve(__dirname, 'lib/services/empty-service.js'),
        // Exclude electron-dependent modules for web builds
        '@/lib/db/electron-db': path.resolve(__dirname, 'lib/services/empty-service.js'),
        '@/lib/auth/mongo-auth-ops': path.resolve(__dirname, 'lib/services/empty-auth-ops.js'),
        '@/lib/mongodb': path.resolve(__dirname, 'lib/services/empty-mongodb.js'),
        // Exclude electron-dependent components and hooks
        '@/app/hooks/use-p2p-sync': path.resolve(__dirname, 'lib/services/empty-hook.ts'),
        '@/app/components/MDNSBrowserComponent': path.resolve(__dirname, 'lib/services/empty-component.tsx'),
      };
    }

         // 📱🖥️ Static builds: Handle Node.js modules and ignore server-only code
     if (process.env.BUILD_TARGET === 'static' || process.env.BUILD_TARGET === 'electron' || process.env.BUILD_TARGET === 'mobile') {
       // API directory is temporarily hidden during build, so no webpack config needed

       // Client-side: Mark Node.js modules as external/false
       if (!isServer) {
         config.resolve.fallback = {
           ...config.resolve.fallback,
           fs: false,
           path: false,
           'fs/promises': false,
           os: false,
           crypto: false,
           stream: false,
           net: false,
           tls: false,
           child_process: false,
           // Fix PouchDB node-fetch issues
           'node-fetch': false,
           'http': false,
           'https': false,
           'url': false,
         };
         
         // Fix PouchDB issues with proper polyfills/fallbacks
         config.resolve.alias = {
           ...config.resolve.alias,
           'node-fetch': path.resolve(__dirname, 'lib/utils/ignore-loader.js'),
           // Ignore problematic server modules
           'mongo-auth-ops': path.resolve(__dirname, 'lib/utils/ignore-loader.js'),
           'mongodb': path.resolve(__dirname, 'lib/utils/ignore-loader.js'),
           'nano': path.resolve(__dirname, 'lib/utils/ignore-loader.js'),
           'bcryptjs': path.resolve(__dirname, 'lib/utils/ignore-loader.js'),
         };
       }

       // Add ignore-loader if not present
       config.resolveLoader.alias = {
         ...config.resolveLoader.alias,
         'ignore-loader': path.resolve(__dirname, 'lib/utils/ignore-loader.js')
       };
     }


    // Exclude electron dependencies from non-electron builds
    if (process.env.BUILD_TARGET !== 'electron' && process.env.BUILD_TARGET !== 'static' && process.env.BUILD_TARGET !== 'mobile') {
      config.externals = config.externals || [];
      if (Array.isArray(config.externals)) {
        config.externals.push('electron');
      }
    }
    
    return config;
  },
  
  // Configure server response headers for security
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
        ],
      },
    ];
  },

  // 🎯 Build configuration for different environments
  typescript: {
    // Allow builds to complete with type errors (can be tightened later)
    ignoreBuildErrors: true,
  },
  
  eslint: {
    // Allow builds to complete with lint errors (can be tightened later)
    ignoreDuringBuilds: true,
  },

  // 🚀 Enable compression for static assets
  compress: true,

  // 🔄 Configure redirects and rewrites
  async redirects() {
    return [
      // Redirect old routes to new structure
      {
        source: '/orders',
        destination: '/protected/ordering',
        permanent: true,
      },
    ];
  },
};

export default nextConfig;