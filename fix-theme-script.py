#!/usr/bin/env python3

import os
import re
import glob

def remove_theme_scripts():
    """Remove problematic theme scripts from all HTML files in electron/app"""
    
    # Find all HTML files in electron/app directory
    html_files = glob.glob('electron/app/**/*.html', recursive=True)
    
    print(f"🔍 Found {len(html_files)} HTML files")
    
    # Pattern to match the problematic theme script
    theme_script_pattern = r'<script>\(\(e,t,r,n,o,a,i,s\)=>\{[^}]+\}\)\([^)]+\)</script>'
    
    processed_count = 0
    modified_count = 0
    
    for file_path in html_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # Remove the theme script
            content = re.sub(theme_script_pattern, '', content)
            
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print(f"✅ Fixed: {file_path}")
                modified_count += 1
            
            processed_count += 1
            
        except Exception as e:
            print(f"❌ Error processing {file_path}: {e}")
    
    print(f"\n📊 Summary:")
    print(f"   Processed: {processed_count} files")
    print(f"   Modified:  {modified_count} files")
    print(f"✅ Theme script cleanup completed!")

if __name__ == "__main__":
    remove_theme_scripts()
