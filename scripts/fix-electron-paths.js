#!/usr/bin/env node

/**
 * Fix Electron Static Asset Paths
 * 
 * This script fixes the mixed asset path issue in Next.js static exports for Electron.
 * It converts all absolute paths (/_next/...) to relative paths (./_next/...)
 * in all HTML files in the electron/app directory.
 */

const fs = require('fs');
const path = require('path');

const ELECTRON_APP_DIR = path.join(__dirname, '..', 'electron', 'app');

function fixPathsInFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Fix all absolute _next paths to relative paths
    const patterns = [
      // CSS links
      { from: /href="\/_next\//g, to: 'href="./_next/' },
      // Script sources
      { from: /src="\/_next\//g, to: 'src="./_next/' },
      // Preload links
      { from: /href="\/_next\/static\//g, to: 'href="./_next/static/' },
      // Any other _next references in JSON or inline scripts
      { from: /"\/_next\/static\//g, to: '"./_next/static/' },
      // Fix webpack runtime references
      { from: /"static\/chunks\//g, to: '"./_next/static/chunks/' },
      // Remove ALL problematic theme scripts that cause forEach errors
      { from: /<script>\(\(e,t,r,n,o,a,i,s\)=>\{[^}]+\}\)\([^)]+\)<\/script>/g, to: '' },
      // Remove any script containing forEach that might cause issues
      { from: /<script>[^<]*\.forEach[^<]*<\/script>/g, to: '' },
      // Fix any remaining absolute paths in webpack chunks
      { from: /publicPath:\s*"\//g, to: 'publicPath: "./' },
      { from: /__webpack_require__\.p\s*=\s*"\//g, to: '__webpack_require__.p = "./' },
    ];
    
    patterns.forEach(pattern => {
      const newContent = content.replace(pattern.from, pattern.to);
      if (newContent !== content) {
        content = newContent;
        modified = true;
      }
    });
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed paths in: ${path.relative(ELECTRON_APP_DIR, filePath)}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ Error fixing paths in ${filePath}:`, error.message);
    return false;
  }
}

function walkDirectory(dir) {
  let fixedCount = 0;
  
  try {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Skip _next directory to avoid modifying source files
        if (item !== '_next') {
          fixedCount += walkDirectory(fullPath);
        }
      } else if (item.endsWith('.html')) {
        if (fixPathsInFile(fullPath)) {
          fixedCount++;
        }
      }
    }
  } catch (error) {
    console.error(`❌ Error reading directory ${dir}:`, error.message);
  }
  
  return fixedCount;
}

function main() {
  console.log('🔧 Fixing Electron static asset paths...');
  console.log(`📁 Target directory: ${ELECTRON_APP_DIR}`);
  
  if (!fs.existsSync(ELECTRON_APP_DIR)) {
    console.error(`❌ Electron app directory not found: ${ELECTRON_APP_DIR}`);
    console.error('   Make sure to run the build process first.');
    process.exit(1);
  }
  
  const fixedCount = walkDirectory(ELECTRON_APP_DIR);
  
  console.log(`\n🎉 Path fixing complete!`);
  console.log(`   Fixed ${fixedCount} HTML files`);
  
  if (fixedCount === 0) {
    console.log('   No files needed fixing (paths were already correct)');
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixPathsInFile, walkDirectory };
