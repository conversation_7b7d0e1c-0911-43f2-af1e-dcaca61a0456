// knowledge:start v4 fix - separate client and server components
import { Metadata, Viewport } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import { Providers } from './providers';
import { LayoutClientWrapper } from '@/components/layout-client-wrapper';
import { StagewiseToolbar } from '@stagewise/toolbar-next';
// Server component doesn't directly import client components that use hooks

const inter = Inter({ subsets: ['latin'] });

const stagewiseConfig = {
  plugins: []
};

export const metadata: Metadata = {
  title: 'Bistro | برنامج تسيير المطاعم في الجزائر - Logiciel de gestion de restaurant en Algérie',
  description: 'Bistro هو برنامج شامل لإدارة وتسيير المطاعم في الجزائر. حلول متكاملة للطلبات، المخزون، والموظفين. Logiciel complet pour la gestion de votre restaurant en Algérie.',
  keywords: ['برنامج تسيير مطاعم', 'ادارة مطاعم', 'الجزائر', 'logiciel de gestion restaurant', 'caisse restaurant', 'algérie', 'bistro', 'restaurant management'],
  applicationName: 'Bistro',
  authors: [{ name: 'Bistro' }],
  creator: 'Bistro',
  publisher: 'Bistro',
  alternates: {
    canonical: 'https://bistro.icu',
  },
  openGraph: {
    title: 'Bistro | أفضل برنامج لتسيير المطاعم في الجزائر',
    description: 'أدِر مطعمك بكفاءة مع Bistro. حلول متكاملة لإدارة الطلبات، المخزون، الموظفين، والمالية. مصمم خصيصاً للسوق الجزائري.',
    url: 'https://bistro.icu',
    siteName: 'Bistro',
    images: [
      {
        url: 'https://bistro.icu/og-image.png', // Replace with your actual OG image URL
        width: 1200,
        height: 630,
        alt: 'Bistro Restaurant Management Software',
      },
    ],
    locale: 'ar_DZ',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Bistro | برنامج تسيير المطاعم في الجزائر',
    description: 'اكتشف Bistro، الحل الأمثل لإدارة مطعمك في الجزائر. سهل، فعال، ومصمم ليلبي كل احتياجاتك.',
    images: ['https://bistro.icu/twitter-image.png'], // Replace with your actual Twitter image URL
  },
};

export const viewport: Viewport = {
  themeColor: '#ffffff',
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5, // Allow some zoom for accessibility
  userScalable: true, // Enable zoom for accessibility
  viewportFit: 'cover', // Handle safe areas on mobile devices
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html suppressHydrationWarning lang="en" dir="ltr" className="h-full overflow-x-hidden">
      <head>
        <link rel="alternate" href="https://bistro.icu" hrefLang="ar-DZ" />
        <link rel="apple-touch-icon" href="/icons/icon-192x192.png" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="mobile-web-app-capable" content="yes" />
        <script 
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "SoftwareApplication",
              "name": "Bistro",
              "operatingSystem": "Windows, macOS, Linux, Android, iOS",
              "applicationCategory": "BusinessApplication",
              "aggregateRating": {
                "@type": "AggregateRating",
                "ratingValue": "4.8",
                "ratingCount": "50"
              },
              "offers": {
                "@type": "Offer",
                "price": "25000",
                "priceCurrency": "DZD",
                "description": "عرض خاص للمستخدمين الأوائل بسعر سنوي مخفض."
              },
              "description": "برنامج Bistro هو الحل الأمثل لإدارة وتسيير المطاعم في الجزائر، يقدم أدوات شاملة للطلبات، المخزون، الموظفين، والتقارير المالية.",
              "url": "https://bistro.icu",
              "screenshot": "https://bistro.icu/screenshot.png"
            })
          }}
        />
      </head>
      <body className="h-full font-tajawal text-foreground mobile-safe-area">
        <Providers>
          <LayoutClientWrapper>
            {children}
          </LayoutClientWrapper>
        </Providers>
        {process.env.NODE_ENV === 'development' && <StagewiseToolbar config={stagewiseConfig} />}
      </body>
    </html>
  );
}
// knowledge:end
