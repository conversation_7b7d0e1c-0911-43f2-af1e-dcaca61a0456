import { NextRequest, NextResponse } from "next/server";
import { verifyJwtAuth } from "@/lib/api-middleware";
// Import removed: import { cleanRestaurantId } from "@/lib/db/couch-connect";
import { staffService } from "@/lib/services/staff-service";

// Server-side version of cleanRestaurantId
function serverCleanRestaurantId(id: string): string {
  if (!id) return '';
  return id.replace(/^restaurant[-_]/, '');
}

export async function POST(request: NextRequest) {
  console.log("API: /api/staff/migrate-permissions - POST request received");

  try {
    // Verify JWT authentication
    console.log("API: Verifying JWT authentication");
    const authResult = await verifyJwtAuth(request);

    if (!authResult.success || !authResult.user) {
      console.error("API: JWT verification failed", {
        success: authResult.success,
        error: authResult.error,
        hasUser: !!authResult.user
      });
      return NextResponse.json(
        { error: authResult.error || "Unauthorized" },
        { status: 401 }
      );
    }

    // Log the authenticated user
    console.log("API: User authenticated", {
      id: authResult.user.id,
      role: authResult.user.role,
      restaurantId: authResult.user.restaurantId
    });

    // Verify that the user has permission to migrate permissions
    // Only restaurant owners or admins should be able to do this
    const { user } = authResult;

    if (user.role !== 'owner' && user.role !== 'admin') {
      console.error("API: Insufficient permissions", {
        userRole: user.role,
        requiredRoles: ['owner', 'admin']
      });
      return NextResponse.json(
        { error: "Insufficient permissions to migrate staff permissions" },
        { status: 403 }
      );
    }

    // Get restaurant ID from user
    const userRestaurantId = user.restaurantId;

    if (!userRestaurantId) {
      return NextResponse.json(
        { error: "Restaurant ID not found in user profile" },
        { status: 400 }
      );
    }

    // Clean the restaurant ID
    const cleanedRestaurantId = serverCleanRestaurantId(userRestaurantId);

    try {
      // Get all staff members from the restaurant database
      const allStaff = await staffService.getAllStaff(cleanedRestaurantId);

      console.log(`API: Found ${allStaff.length} staff members in restaurant ${cleanedRestaurantId}`);

      // Filter staff members with user accounts
      const staffWithAccounts = allStaff.filter(member => member.hasUserAccount && member.userId);

      console.log(`API: Found ${staffWithAccounts.length} staff members with user accounts`);

      if (staffWithAccounts.length === 0) {
        return NextResponse.json({
          success: true,
          message: "No staff members with user accounts found to migrate permissions for",
          migratedCount: 0
        });
      }

      // Track migration results
      const migrationResults = {
        total: staffWithAccounts.length,
        success: 0,
        failed: 0,
        details: [] as Array<{
          staffId: string;
          name: string;
          userId: string | null;
          success: boolean;
          error?: string;
        }>
      };

      // Get CouchDB configuration
      const NEXT_PUBLIC_COUCHDB_URL = process.env.NEXT_PUBLIC_COUCHDB_URL;
      const COUCHDB_USER = process.env.NEXT_PUBLIC_COUCHDB_USER;
      const COUCHDB_PASSWORD = process.env.NEXT_PUBLIC_COUCHDB_PASSWORD;

      if (!NEXT_PUBLIC_COUCHDB_URL || !COUCHDB_USER || !COUCHDB_PASSWORD) {
        return NextResponse.json(
          { error: "CouchDB configuration missing" },
          { status: 500 }
        );
      }

      // Create auth header for CouchDB
      const authHeader = `Basic ${Buffer.from(`${COUCHDB_USER}:${COUCHDB_PASSWORD}`).toString('base64')}`;

      // Process each staff member
      for (const staff of staffWithAccounts) {
        try {
          console.log(`API: Processing staff member ${staff.name} (ID: ${staff.id}, userId: ${staff.userId})`);

          // Skip if no userId
          if (!staff.userId) {
            migrationResults.details.push({
              staffId: staff.id,
              name: staff.name,
              userId: null,
              success: false,
              error: "No user ID found"
            });
            migrationResults.failed++;
            continue;
          }

          // Fetch user document from CouchDB
          const userDocResponse = await fetch(`${NEXT_PUBLIC_COUCHDB_URL}/restaurant-users/${staff.userId}`, {
            headers: {
              'Authorization': authHeader,
              'Accept': 'application/json'
            }
          });

          if (!userDocResponse.ok) {
            console.error(`API: User document not found in CouchDB for userId: ${staff.userId}`);
            migrationResults.details.push({
              staffId: staff.id,
              name: staff.name,
              userId: staff.userId,
              success: false,
              error: `User document not found: ${userDocResponse.status}`
            });
            migrationResults.failed++;
            continue;
          }

          const userDoc = await userDocResponse.json();

          // Extract permissions from the user document
          let permissions = userDoc.permissions;

          if (!permissions && userDoc.metadata?.permissions) {
            permissions = userDoc.metadata.permissions;
            console.log(`API: Using permissions from metadata for user ${staff.userId}`);
          }

          if (!permissions) {
            console.warn(`API: No permissions found for user ${staff.userId}`);
            // Use default permissions (all false)
            permissions = {
              pages: {
                menu: false,
                orders: false,
                finance: false,
                inventory: false,
                staff: false,
                settings: false,
                suppliers: false
              }
            };
          }

          // Update the staff member with the permissions
          const updatedStaff = {
            ...staff,
            permissions
          };

          // Save the updated staff member
          await staffService.updateStaff(updatedStaff, cleanedRestaurantId);

          console.log(`API: Successfully migrated permissions for staff ${staff.name} (ID: ${staff.id})`);

          migrationResults.details.push({
            staffId: staff.id,
            name: staff.name,
            userId: staff.userId,
            success: true
          });

          migrationResults.success++;
        } catch (error) {
          console.error(`API: Error migrating permissions for staff ${staff.id}:`, error);

          migrationResults.details.push({
            staffId: staff.id,
            name: staff.name,
            userId: staff.userId,
            success: false,
            error: error instanceof Error ? error.message : String(error)
          });

          migrationResults.failed++;
        }
      }

      return NextResponse.json({
        success: true,
        message: `Migrated permissions for ${migrationResults.success} out of ${migrationResults.total} staff members`,
        results: migrationResults
      });
    } catch (error) {
      console.error("API: Error migrating staff permissions", error);

      return NextResponse.json(
        { error: error instanceof Error ? error.message : "Failed to migrate staff permissions" },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("API: Unhandled error in migrate staff permissions request", error);

    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Internal server error" },
      { status: 500 }
    );
  }
}
