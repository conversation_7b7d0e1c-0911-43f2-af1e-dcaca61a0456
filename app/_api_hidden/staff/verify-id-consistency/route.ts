import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { verifyJwtAuth } from "@/lib/api-middleware";
// import { verifyStaffIdConsistency, fixStaffIdConsistency } from "@/lib/utils/staff-id-consistency";

// Validation schema for the request body
const requestSchema = z.object({
  staffId: z.string().uuid({
    message: "Staff ID must be a valid UUID"
  }),
  restaurantId: z.string().min(1, {
    message: "Restaurant ID is required"
  }),
  fix: z.boolean().optional().default(false)
});

/**
 * API endpoint to verify and optionally fix staff ID consistency between auth and staff documents
 * This is useful for debugging and fixing issues with staff permissions not being fetched during login
 */
export async function POST(request: NextRequest) {
  try {
    // Verify JWT authentication
    const authResult = await verifyJwtAuth(request);
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error || "Authentication required" },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    
    // Validate request body
    const validation = requestSchema.safeParse(body);
    if (!validation.success) {
      const errorMessage = validation.error.errors.map(e => `${e.path}: ${e.message}`).join(", ");
      return NextResponse.json(
        { error: `Invalid request: ${errorMessage}` },
        { status: 400 }
      );
    }

    const { staffId, restaurantId, fix } = validation.data;

    // Verify staff ID consistency
    // const verificationResult = await verifyStaffIdConsistency(staffId, restaurantId);
    const verificationResult = { success: false, message: 'verifyStaffIdConsistency not implemented' };

    // If fix is requested and there are consistency issues, fix them
    let fixResult = null;
    if (fix && !verificationResult.success) {
      // fixResult = await fixStaffIdConsistency(staffId, restaurantId);
      fixResult = { success: false, message: 'fixStaffIdConsistency not implemented' };
    }

    // Return the verification and fix results
    return NextResponse.json({
      verification: verificationResult,
      fix: fixResult
    });
  } catch (error) {
    console.error("Error verifying staff ID consistency:", error);
    return NextResponse.json(
      { error: `Error verifying staff ID consistency: ${error instanceof Error ? error.message : String(error)}` },
      { status: 500 }
    );
  }
}
