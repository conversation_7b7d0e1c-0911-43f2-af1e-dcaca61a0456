import { NextRequest, NextResponse } from "next/server";
import { getMongoUserByUsername } from '@/lib/auth/mongo-auth-ops';

/**
 * Public endpoint to check if a username is available globally
 * This endpoint doesn't require authentication and checks global username uniqueness
 */
export async function GET(request: NextRequest) {
  console.log('[API] MONGODB_URI (masked):', process.env.MONGODB_URI ? process.env.MONGODB_URI.replace(/\/\/.*:.*@/, '//USER:PASSWORD@') : 'NOT SET');
  try {
    // Get the username from the query params
    const url = new URL(request.url);
    const username = url.searchParams.get('username');
    
    if (!username) {
      return NextResponse.json({ error: "Username parameter is required" }, { status: 400 });
    }

    // Query MongoDB to see if the username already exists GLOBALLY (no restaurant filter)
    const user = await getMongoUserByUsername(username); // No restaurantId = global search
    const available = !user;

    return NextResponse.json({ 
      available,
      message: available ? 
        "Username is available" : 
        "Username is already taken",
      context: 'global'
    });
  } catch (error) {
    console.error("Error checking username availability:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "An unknown error occurred" },
      { status: 500 }
    );
  }
}
