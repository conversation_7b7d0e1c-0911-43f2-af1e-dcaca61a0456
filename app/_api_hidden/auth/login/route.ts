import { NextRequest, NextResponse } from 'next/server';
import { getMongoUserByEmail } from '@/lib/auth/mongo-auth-ops'; // Using email for owner/admin login by default
import { verifyPassword, generateToken, User as JwtUser } from '@/lib/auth/new-auth-service';

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    console.log('[API Login] Received request body:', body);

    const { identifier, password } = body;
    const email = identifier;

    console.log('[API Login] Processing login request for identifier:', identifier);

    if (!email || !password) {
      console.log('[API Login] Missing identifier (as email) or password in request.');
      return NextResponse.json(
        { error: 'Identifier (email) and password are required' },
        { status: 400 }
      );
    }

    // 1. Fetch user from MongoDB by email
    const user = await getMongoUserByEmail(email);

    if (!user) {
      console.log(`[API Login] User not found for email: ${email}`);
      return NextResponse.json({ error: 'Invalid credentials - user not found' }, { status: 401 });
    }

    // 2. Verify password
    const isValidPassword = await verifyPassword(password, user.password);

    if (!isValidPassword) {
      console.log(`[API Login] Invalid password for user: ${email}`);
      return NextResponse.json({ error: 'Invalid credentials - password incorrect' }, { status: 401 });
    }

    // 3. Prepare user object for JWT
    const userForToken: JwtUser = {
      id: user._id,
      name: user.name,
      email: user.email,
      username: user.username, // Include if available
      role: user.role,
      restaurantId: user.restaurantId,
      permissions: user.permissions, // Default permissions from MongoDB user doc
      metadata: user.metadata,
    };

    // 4. Generate JWT
    const token = generateToken(userForToken);

    return NextResponse.json({
      message: 'Login successful',
      token,
      user: { // Return non-sensitive user info
        id: user._id,
        name: user.name,
        email: user.email,
        username: user.username,
        role: user.role,
        restaurantId: user.restaurantId,
      }
    }, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      }
    });

  } catch (error) {
    console.error('[API Login] Error:', error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json({ error: `Login failed: ${errorMessage}` }, { 
      status: 500,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      }
    });
  }
}

// Handle CORS preflight requests
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
}