import { NextRequest, NextResponse } from 'next/server';
import { generateToken, verifyToken } from '@/lib/auth/new-auth-service';
import { initializeV4Database, getAllStaff } from '@/lib/db/v4';

export async function POST(req: NextRequest) {
  try {
    // Parse request body
    const body = await req.json();
    const { refreshToken } = body;

    // Validate input
    if (!refreshToken) {
      return NextResponse.json(
        { error: 'Refresh token is required' },
        { status: 400 }
      );
    }

    // Verify refresh token
    const payload = verifyToken(refreshToken);

    if (!payload) {
      return NextResponse.json(
        { error: 'Invalid refresh token' },
        { status: 401 }
      );
    }

    // Extract user ID and potentially other details from token payload
    const userId = payload.sub?.replace('refresh_', '') || 'unknown_user';
    const payloadRestaurantId = (payload as any).restaurantId;
    const payloadRole = (payload as any).role;
    const payloadName = (payload as any).name;
    const payloadEmail = (payload as any).email;
    const payloadMetadata = (payload as any).metadata || {};

    // Construct userDoc. Prioritize info from token payload, then defaults.
    // You MUST ensure these fields are correctly populated for your V4 system.
    const userDoc: any = {
      _id: userId,
      role: payloadRole || 'staff', // Example: Default role if not in token
      restaurantId: payloadRestaurantId || 'defaultRestaurant', // Example: Default restaurant if not in token
      name: payloadName || 'Unknown User', // Example: Default name if not in token
      email: payloadEmail || '<EMAIL>', // Example: Default email if not in token
      metadata: payloadMetadata
    };

    // If critical info like userId is missing after attempting to parse token.
    if (userId === 'unknown_user') {
        console.error('REFRESH: Critical user information (userId) missing from token payload.');
        return NextResponse.json(
            { error: 'Invalid token payload: User ID missing' },
            { status: 401 }
        );
    }
    // If restaurantId is critical and not found (remove this check if restaurantId can be optional or derived later)
    if (userDoc.restaurantId === 'defaultRestaurant' && !payloadRestaurantId) {
        console.warn(`REFRESH: restaurantId not found in token payload for user ${userId}, using default. This might lead to issues.`);
    }

      // For staff users, fetch permissions from the restaurant database
      let permissions = null;

      if (userDoc.role === 'staff' && userDoc.restaurantId) {
        console.log(`REFRESH: Fetching permissions for staff user ${userDoc._id} in restaurant ${userDoc.restaurantId}`)
        
        try {
          // Initialize database for this restaurant
          await initializeV4Database(userDoc.restaurantId);
          
          // Get ALL staff members and find the one with matching userId
          console.log('REFRESH: Getting all staff to find matching userId...');
          const allStaff = await getAllStaff();
          console.log('REFRESH: Total staff members found:', allStaff.length);
          
          // Find staff document where userId matches user's _id
          const staffMember = allStaff.find(staff => staff.userId === userDoc._id);

          if (staffMember?.permissions) {
            console.log(`REFRESH: ✅ Found staff member ${staffMember.name} with permissions.`);
            console.log('REFRESH: Staff permissions:', JSON.stringify(staffMember.permissions, null, 2));
            
            // Use the raw permissions from PouchDB - don't convert to array format
            // The permissions context expects the raw object format
            permissions = staffMember.permissions;
            
            console.log('REFRESH: ✅ Using raw permissions:', JSON.stringify(permissions, null, 2));

          } else {
            console.error('REFRESH: ❌ Staff member not found or has no permissions');
            console.log('REFRESH: Looking for userId:', userDoc._id);
            console.log('REFRESH: Available staff members:', allStaff.map(s => ({
              docId: s._id,
              id: s.id,
              name: s.name,
              userId: s.userId,
              hasPermissions: !!s.permissions
            })));
            
            // Set null permissions if staff not found - let the UI handle empty state
            permissions = null;
          }
        } catch (error) {
          console.error(`REFRESH: ❌ Error fetching staff permissions:`, error);
          // Set null permissions on error - let the UI handle empty state
          permissions = null;
        }
      } else if (userDoc.role === 'staff') {
        console.log(`REFRESH: Staff user ${userDoc._id} has no restaurantId - cannot fetch permissions`);
        // Set null permissions if no restaurant - let the UI handle empty state
        permissions = null;
      }

      // Create user object for token
      const userForToken = {
        id: userDoc._id,
        name: userDoc.name,
        email: userDoc.email,
        role: userDoc.role,
        restaurantId: userDoc.restaurantId,
        permissions: permissions,
        metadata: userDoc.metadata
      };

      // Generate new access token
      const token = generateToken(userForToken);

      // Return new access token and user object
      return NextResponse.json({
        token,
        user: userForToken
      }, {
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        }
      });

  } catch (error: any) {
    console.error('Refresh token processing error:', error);
    // Check if it's a token verification error specifically
    if (error.name === 'JsonWebTokenError' || error.message.includes('Invalid token')) {
         return NextResponse.json({ error: 'Invalid refresh token' }, { 
           status: 401,
           headers: {
             'Access-Control-Allow-Origin': '*',
             'Access-Control-Allow-Methods': 'POST, OPTIONS',
             'Access-Control-Allow-Headers': 'Content-Type, Authorization',
           }
         });
    }
    return NextResponse.json(
      { error: 'Internal server error during token refresh' },
      { 
        status: 500,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        }
      }
    );
  }
}

// Handle CORS preflight requests
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
}