import { NextRequest, NextResponse } from 'next/server';
import { createMongoUser } from '@/lib/auth/mongo-auth-ops';
import { v4 as uuidv4 } from 'uuid';

export async function GET(req: NextRequest) {
  try {
    // Create a test owner user
    const restaurantId = `restaurant:${uuidv4()}`;
    
    const ownerResult = await createMongoUser({
      name: 'Test Owner',
      email: '<EMAIL>',
      plaintextPassword: 'password123',
      role: 'owner',
      restaurantId: restaurantId,
    });

    // Create a test staff user
    const staffResult = await createMongoUser({
      name: 'Test Staff',
      username: 'staff',
      plaintextPassword: 'password123',
      role: 'staff',
      restaurantId: restaurantId,
    });

    return NextResponse.json({
      message: 'Test users created successfully',
      owner: ownerResult.success ? {
        email: '<EMAIL>',
        password: 'password123',
        id: ownerResult.userId,
        restaurantId: restaurantId
      } : { error: ownerResult.error },
      staff: staffResult.success ? {
        username: 'staff',
        password: 'password123',
        id: staffResult.userId,
        restaurantId: restaurantId
      } : { error: staffResult.error }
    });
  } catch (error) {
    console.error('Error creating test users:', error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json({ error: `Failed to create test users: ${errorMessage}` }, { status: 500 });
  }
}
