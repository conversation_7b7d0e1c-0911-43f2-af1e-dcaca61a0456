import { NextRequest, NextResponse } from 'next/server';
import { createMongoUser } from '@/lib/auth/mongo-auth-ops';
import { generateToken, User as JwtUser } from '@/lib/auth/new-auth-service';
import { v4 as uuidv4 } from 'uuid';

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { name, email, password, phoneNumber } = body;
    
    if (!name || !email || !password || !phoneNumber) {
      return NextResponse.json(
        { error: 'Name, email, password, and phoneNumber are required' },
        { status: 400 }
      );
    }

    const restaurantIdForNewOwner = `restaurant:${uuidv4()}`;

    const mongoUserResult = await createMongoUser({
      name,
      email,
      plaintextPassword: password,
      role: 'owner',
      restaurantId: restaurantIdForNewOwner,
    });

    if (!mongoUserResult.success || !mongoUserResult.userId || !mongoUserResult.user) {
      return NextResponse.json(
        { error: `User registration failed: ${mongoUserResult.error || 'Unknown error'}` },
        { status: 500 }
      );
    }

    const createdUser = mongoUserResult.user;

    console.log(`TODO: Create restaurant entry for ${phoneNumber} with ID ${restaurantIdForNewOwner}`);

    const userForToken: JwtUser = {
      id: createdUser._id,
      name: createdUser.name,
      email: createdUser.email,
      role: createdUser.role,
      restaurantId: createdUser.restaurantId,
      permissions: createdUser.permissions,
      metadata: createdUser.metadata,
    };

    const token = generateToken(userForToken);

    return NextResponse.json({
      message: 'User registered successfully',
      token,
      user: {
        id: createdUser._id,
        name: createdUser.name,
        email: createdUser.email,
        role: createdUser.role,
        restaurantId: createdUser.restaurantId,
      }
    }, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      }
    });

  } catch (error) {
    console.error('[API Register] Error:', error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json({ error: `Registration failed: ${errorMessage}` }, { 
      status: 500,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      }
    });
  }
}

// Handle CORS preflight requests
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
}