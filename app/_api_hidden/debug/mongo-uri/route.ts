import { NextResponse } from 'next/server';

/**
 * Helper to mask credentials in MongoDB URI
 */
function maskCredentials(url: string): string {
  try {
    // Handle SRV and non-SRV URIs
    const isSrvUri = url.includes('+srv') || url.includes('mongodb.net');

    // For standard URIs, use URL parsing
    if (!isSrvUri) {
      try {
        const parsedUrl = new URL(url);
        if (parsedUrl.username || parsedUrl.password) {
          parsedUrl.username = 'USER';
          parsedUrl.password = 'PASSWORD';
        }
        return parsedUrl.toString();
      } catch (parseError) {
        console.warn('[API] Error parsing URI with URL constructor');
      }
    }

    // Fallback to regex replacement for both SRV and standard URIs
    return url.replace(/mongodb(?:[+a-z]*):\/\/[^:]*:[^@]*@/, 'mongodb://USER:PASSWORD@');
  } catch (e) {
    console.warn('[API] Error masking MongoDB URI');
    // Ultimate fallback
    return 'mongodb://[MASKED_URI]';
  }
}

export async function GET() {
  try {
    if (!process.env.MONGODB_URI) {
      return NextResponse.json({
        uri: null,
        error: 'MONGODB_URI environment variable is not set'
      });
    }
    
    // Mask the URI to avoid exposing credentials
    const maskedUri = maskCredentials(process.env.MONGODB_URI);
    
    return NextResponse.json({
      uri: maskedUri
    });
  } catch (error) {
    console.error('[API] Error getting MongoDB URI:', error);
    return NextResponse.json({
      uri: null,
      error: error instanceof Error ? error.message : 'Unknown error getting MongoDB URI'
    });
  }
} 