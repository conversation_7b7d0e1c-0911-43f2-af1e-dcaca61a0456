import { NextResponse } from 'next/server';
import clientPromise from '@/lib/mongodb';
import { ObjectId } from 'mongodb';

export async function GET() {
  try {
    console.log('[API] MongoDB debug query initiated');
    
    const client = await clientPromise;
    const db = client.db('resto');
    
    // First check if the users collection exists
    const collections = await db.listCollections({ name: 'users' }).toArray();
    if (collections.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'Users collection does not exist',
        results: null
      });
    }
    
    // Test query to get up to 5 usernames (if any)
    const usersCollection = db.collection('users');
    const usersSample = await usersCollection
      .find({}, { projection: { username: 1, email: 1, role: 1, _id: 0 } })
      .limit(5)
      .toArray();
    
    // If no users found, return info about available collections
    if (usersSample.length === 0) {
      console.log('[API] No users found in sample query, listing collections');
      const allCollections = await db.listCollections().toArray();
      const collectionNames = allCollections.map(c => c.name);
      
      return NextResponse.json({
        success: true,
        message: 'No users found, but connection working.',
        results: {
          collections: collectionNames,
          users: []
        }
      });
    }
    
    // Return successful result
    return NextResponse.json({
      success: true,
      results: {
        users: usersSample.map(user => {
          // Mask emails for privacy
          if (user.email) {
            const parts = user.email.split('@');
            if (parts.length === 2) {
              const localPart = parts[0];
              const maskedLocal = localPart.length > 3 
                ? `${localPart.substring(0, 2)}${'*'.repeat(localPart.length - 2)}`
                : `${localPart.substring(0, 1)}${'*'.repeat(localPart.length - 1)}`;
              user.email = `${maskedLocal}@${parts[1]}`;
            }
          }
          return user;
        })
      }
    });
    
  } catch (error) {
    console.error('[API] MongoDB debug query error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error executing MongoDB query',
      results: null
    });
  }
} 