import { NextRequest, NextResponse } from 'next/server';
import { GoogleDriveService } from '@/lib/services/google-drive-service';

export async function POST(request: NextRequest) {
  try {
    console.log('📤 Google Drive upload API called');

    const formData = await request.formData();
    const file = formData.get('file') as File;
    const restaurantId = formData.get('restaurantId') as string;
    const transactionId = formData.get('transactionId') as string | null;
    const fileType = formData.get('fileType') as string | null;

    if (!file) {
      return NextResponse.json(
        { success: false, error: 'No file provided' },
        { status: 400 }
      );
    }

    if (!restaurantId) {
      return NextResponse.json(
        { success: false, error: 'Restaurant ID is required' },
        { status: 400 }
      );
    }

    // Initialize Google Drive service
    const googleDriveService = new GoogleDriveService();
    const initialized = await googleDriveService.initializeFromMongoDB(restaurantId);

    if (!initialized) {
      return NextResponse.json(
        { success: false, error: 'Google Drive not configured for this restaurant' },
        { status: 400 }
      );
    }

    // Convert File to Buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // Upload file
    const result = await googleDriveService.uploadFile(
      buffer,
      file.name,
      restaurantId,
      {
        transactionId: transactionId || undefined,
        fileType: fileType || undefined
      }
    );

    if (result.success) {
      console.log('✅ File uploaded successfully:', result.fileId);
      return NextResponse.json({
        success: true,
        fileId: result.fileId,
        webViewLink: result.webViewLink
      });
    } else {
      console.error('❌ Upload failed:', result.error);
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('❌ Google Drive upload API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Upload failed' 
      },
      { status: 500 }
    );
  }
} 