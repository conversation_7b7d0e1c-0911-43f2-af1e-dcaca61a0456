import { NextRequest, NextResponse } from 'next/server';
import { google } from 'googleapis';
import { saveGoogleDriveConfig } from '@/lib/db/mongo/restaurant-settings-ops';

// Pre-configured OAuth credentials for easy setup
const EASY_OAUTH_CONFIG = {
  clientId: process.env.GOOGLE_OAUTH_CLIENT_ID || '102537660956-857id6odqfnou4kl6o1cbtl6b4sqgalv.apps.googleusercontent.com',
  clientSecret: process.env.GOOGLE_OAUTH_CLIENT_SECRET || 'GOCSPX-example-secret-key',
  redirectUri: process.env.GOOGLE_OAUTH_REDIRECT_URI || `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/google-drive/oauth/callback`
};

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const code = searchParams.get('code');
    const error = searchParams.get('error');
    
    console.log('🔄 Processing OAuth callback...');
    
    // Handle OAuth errors
    if (error) {
      console.error('❌ OAuth error:', error);
      return createErrorResponse('Authorization was denied or failed');
    }
    
    if (!code) {
      console.error('❌ No authorization code received');
      return createErrorResponse('No authorization code received');
    }
    
    // Create OAuth2 client
    const oauth2Client = new google.auth.OAuth2(
      EASY_OAUTH_CONFIG.clientId,
      EASY_OAUTH_CONFIG.clientSecret,
      EASY_OAUTH_CONFIG.redirectUri
    );
    
    // Exchange authorization code for tokens
    console.log('🔄 Exchanging authorization code for tokens...');
    const { tokens } = await oauth2Client.getToken(code);
    
    if (!tokens.refresh_token) {
      console.error('❌ No refresh token received');
      return createErrorResponse('Failed to get refresh token. Please try again.');
    }
    
    // Set credentials to get user info
    oauth2Client.setCredentials(tokens);
    
    // Get user information
    console.log('👤 Getting user information...');
    const oauth2 = google.oauth2({ version: 'v2', auth: oauth2Client });
    const userInfo = await oauth2.userinfo.get();
    
    // Test Drive API access
    console.log('🔄 Testing Google Drive API access...');
    const drive = google.drive({ version: 'v3', auth: oauth2Client });
    await drive.about.get({ fields: 'user' });
    
    // For demo purposes, we'll use a default restaurant ID
    // In a real app, this would come from the user's session
    const restaurantId = 'demo-restaurant-id';
    
    // Save configuration to MongoDB
    console.log('💾 Saving Google Drive configuration...');
    const saveResult = await saveGoogleDriveConfig(restaurantId, {
      type: 'oauth',
      credentials: {
        clientId: EASY_OAUTH_CONFIG.clientId,
        clientSecret: EASY_OAUTH_CONFIG.clientSecret,
        refreshToken: tokens.refresh_token,
        accessToken: tokens.access_token
      },
      setupBy: 'easy-setup'
    });
    
    if (!saveResult.success) {
      console.error('❌ Failed to save configuration:', saveResult.error);
      return createErrorResponse('Failed to save configuration');
    }
    
    console.log('✅ Google Drive setup completed successfully!');
    
    // Return success page that communicates with parent window
    return createSuccessResponse(userInfo.data);
    
  } catch (error) {
    console.error('❌ OAuth callback error:', error);
    return createErrorResponse(
      error instanceof Error ? error.message : 'Unknown error occurred'
    );
  }
}

function createSuccessResponse(userInfo: any) {
  const successHtml = `
    <!DOCTYPE html>
    <html>
      <head>
        <title>Google Drive Setup - Success</title>
        <style>
          body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            margin: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          }
          .success-container {
            text-align: center;
            padding: 2rem;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            max-width: 400px;
            animation: slideIn 0.5s ease-out;
          }
          @keyframes slideIn {
            from {
              opacity: 0;
              transform: translateY(-20px);
            }
            to {
              opacity: 1;
              transform: translateY(0);
            }
          }
          .success-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            animation: bounce 1s ease-in-out;
          }
          @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
              transform: translateY(0);
            }
            40% {
              transform: translateY(-10px);
            }
            60% {
              transform: translateY(-5px);
            }
          }
          .success-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #059669;
            margin-bottom: 0.5rem;
          }
          .success-message {
            color: #6b7280;
            margin-bottom: 1rem;
            line-height: 1.5;
          }
          .user-info {
            background: #f3f4f6;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
          }
          .user-email {
            font-weight: 600;
            color: #374151;
          }
          .close-button {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.875rem;
            font-weight: 600;
            transition: all 0.2s;
          }
          .close-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
          }
          .countdown {
            font-size: 0.75rem;
            color: #9ca3af;
            margin-top: 1rem;
          }
        </style>
      </head>
      <body>
        <div class="success-container">
          <div class="success-icon">🎉</div>
          <h1 class="success-title">Setup Complete!</h1>
          <p class="success-message">
            Your Google Drive has been connected successfully.
          </p>
          <div class="user-info">
            <div class="user-email">${userInfo.email || 'Connected Account'}</div>
            <div style="font-size: 0.875rem; color: #6b7280; margin-top: 0.25rem;">
              ${userInfo.name || 'Google Account'}
            </div>
          </div>
          <button class="close-button" onclick="closeWindow()">Continue</button>
          <div class="countdown">This window will close automatically in <span id="countdown">5</span> seconds</div>
        </div>
        
        <script>
          let countdown = 5;
          const countdownElement = document.getElementById('countdown');
          
          function updateCountdown() {
            countdown--;
            if (countdownElement) {
              countdownElement.textContent = countdown;
            }
            if (countdown <= 0) {
              closeWindow();
            }
          }
          
          function closeWindow() {
            if (window.opener) {
              window.opener.postMessage({
                type: 'GOOGLE_OAUTH_SUCCESS',
                user: {
                  email: '${userInfo.email || ''}',
                  name: '${userInfo.name || ''}'
                }
              }, window.location.origin);
            }
            window.close();
          }
          
          // Start countdown
          const interval = setInterval(() => {
            updateCountdown();
            if (countdown <= 0) {
              clearInterval(interval);
            }
          }, 1000);
        </script>
      </body>
    </html>
  `;
  
  return new NextResponse(successHtml, {
    headers: {
      'Content-Type': 'text/html',
    },
  });
}

function createErrorResponse(errorMessage: string) {
  const errorHtml = `
    <!DOCTYPE html>
    <html>
      <head>
        <title>Google Drive Setup - Error</title>
        <style>
          body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            margin: 0;
            background: #fef2f2;
          }
          .error-container {
            text-align: center;
            padding: 2rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            max-width: 400px;
            border: 1px solid #fecaca;
          }
          .error-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
          }
          .error-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #dc2626;
            margin-bottom: 0.5rem;
          }
          .error-message {
            color: #6b7280;
            margin-bottom: 1.5rem;
            line-height: 1.5;
          }
          .close-button {
            background: #dc2626;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.875rem;
          }
          .close-button:hover {
            background: #b91c1c;
          }
        </style>
      </head>
      <body>
        <div class="error-container">
          <div class="error-icon">❌</div>
          <h1 class="error-title">Setup Failed</h1>
          <p class="error-message">
            ${errorMessage}
          </p>
          <button class="close-button" onclick="closeWindow()">Close</button>
        </div>
        
        <script>
          function closeWindow() {
            if (window.opener) {
              window.opener.postMessage({
                type: 'GOOGLE_OAUTH_ERROR',
                error: '${errorMessage}'
              }, window.location.origin);
            }
            window.close();
          }
          
          // Auto-close after 10 seconds
          setTimeout(closeWindow, 10000);
        </script>
      </body>
    </html>
  `;
  
  return new NextResponse(errorHtml, {
    status: 400,
    headers: {
      'Content-Type': 'text/html',
    },
  });
}