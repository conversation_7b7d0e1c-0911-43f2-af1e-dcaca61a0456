import { NextRequest, NextResponse } from 'next/server';
import { GoogleDriveService } from '@/lib/services/google-drive-service';

export async function POST(request: NextRequest) {
  try {
    console.log('🧪 Google Drive test API called');

    const { restaurantId } = await request.json();

    if (!restaurantId) {
      return NextResponse.json(
        { success: false, error: 'Restaurant ID is required' },
        { status: 400 }
      );
    }

    // Initialize Google Drive service
    const googleDriveService = new GoogleDriveService();
    const initialized = await googleDriveService.initializeFromMongoDB(restaurantId);

    if (!initialized) {
      return NextResponse.json(
        { success: false, error: 'Google Drive not configured for this restaurant' },
        { status: 400 }
      );
    }

    // Test connection
    const result = await googleDriveService.testConnection();

    if (result.success) {
      console.log('✅ Google Drive connection test successful');
      return NextResponse.json({
        success: true,
        user: result.user,
        quota: result.quota
      });
    } else {
      console.error('❌ Google Drive connection test failed:', result.error);
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('❌ Google Drive test API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Connection test failed' 
      },
      { status: 500 }
    );
  }
} 