"use client";

import React, { useState, useReducer, use<PERSON><PERSON>back, useMemo, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card, CardContent } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/components/ui/use-toast";
import { cn } from "@/lib/utils";
import {
  Search,
  Utensils,
  Plus,
  Minus,
  X,
  ShoppingCart,
  Check,
  Pizza,
  StickyNote,
  ChevronDown,
  AlertCircle,
  Package,
  Edit2
} from "lucide-react";

// Hooks and utilities
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { useMenuV4 } from '@/lib/hooks/use-menu-v4';
import { useTableDB } from '@/lib/hooks/useTableDB';
import { useOrderV4 } from '@/lib/hooks/use-order-v4';
import { useSupplements } from '@/lib/hooks/useSupplements';

// Types
import { MenuItem } from '@/lib/db/v4/schemas/menu-schema';
import { OrderType } from '@/lib/types/order-types';
import { OrderDocument } from '@/lib/db/v4/schemas/order-schema';

// Order types and interfaces (following NewOrderingInterface conventions)
interface OrderAddon {
  id: string;
  name: string;
  price: number;
}

interface PizzaQuarter {
  menuItemId: string;
  name: string;
  price: number;
  size?: string;
}

interface OrderItem {
  id: string;
  menuItemId: string;
  name: string;
  size: string;
  price: number;
  quantity: number;
  addons: OrderAddon[];
  notes: string;
  categoryId: string;
  compositeType?: 'pizza_quarters';
  quarters?: PizzaQuarter[];
}

interface OrderState {
  items: OrderItem[];
  total: number;
  orderType: OrderType;
  tableId: string;
  notes: string;
}

interface UiState {
  selectedCategory: string;
  searchQuery: string;
  selectedItemForCustomization: string | null;
  selectedItemSizes: {[key: string]: string};
  selectedAddons: {[key: string]: Set<string>};
  itemNotes: {[key: string]: string};
  showTableSelection: boolean;
  showCartSummary: boolean;
  showCustomization: boolean;
  lastAddedItem: string | null;
  pizzaPartition: 'half' | 'quarter';
  pizzaQuarters: (PizzaQuarter | null)[];
  editingItemId: string | null;
}

// Initial states
const initialOrderState: OrderState = {
  items: [],
  total: 0,
  orderType: 'dine-in',
  tableId: '',
  notes: ''
};

const initialUiState: UiState = {
  selectedCategory: '',
  searchQuery: '',
  selectedItemForCustomization: null,
  selectedItemSizes: {},
  selectedAddons: {},
  itemNotes: {},
  showTableSelection: false,
  showCartSummary: false,
  showCustomization: false,
  lastAddedItem: null,
  pizzaPartition: 'quarter',
  pizzaQuarters: [null, null, null, null],
  editingItemId: null
};

// Calculate total helper
const calculateTotal = (items: OrderItem[]): number => {
  return items.reduce((total, item) => {
    const itemTotal = item.price * item.quantity;
    const addonsTotal = item.addons.reduce((sum, addon) => sum + addon.price, 0) * item.quantity;
    return total + itemTotal + addonsTotal;
  }, 0);
};

// Order reducer actions (following NewOrderingInterface pattern)
type OrderAction =
  | { type: 'INITIALIZE_ORDER' }
  | { type: 'ADD_ITEM', payload: { item: MenuItem, size: string, addons: OrderAddon[], notes: string, categoryId: string } }
  | { type: 'ADD_CUSTOM_PIZZA', payload: { quarters: PizzaQuarter[], size: string, notes: string, categoryId: string, pricingMethod: 'max' | 'average' } }
  | { type: 'UPDATE_ITEM', payload: { itemId: string, updates: Partial<OrderItem> } }
  | { type: 'INCREMENT_QUANTITY', payload: { itemId: string } }
  | { type: 'DECREMENT_QUANTITY', payload: { itemId: string } }
  | { type: 'REMOVE_ITEM', payload: { itemId: string } }
  | { type: 'SET_TABLE', payload: { tableId: string } }
  | { type: 'SET_NOTES', payload: { notes: string } };

// Order reducer (following NewOrderingInterface pattern)
const orderReducer = (state: OrderState, action: OrderAction): OrderState => {
  switch (action.type) {
    case 'INITIALIZE_ORDER':
      return initialOrderState;
      
    case 'ADD_ITEM': {
      const { item, size, addons, notes, categoryId } = action.payload;
      const price = item.prices[size] || Object.values(item.prices)[0] || 0;
      const uniqueId = `order_item_${Date.now()}_${Math.floor(Math.random() * 10000)}`;
      
      const newItem: OrderItem = {
        id: uniqueId,
        menuItemId: item.id,
        name: item.name,
        size: size,
        price: price,
        quantity: 1,
        addons: addons,
        notes: notes,
        categoryId: categoryId
      };
      
      const updatedItems = [...state.items, newItem];
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }
      
    case 'ADD_CUSTOM_PIZZA': {
      const { quarters, size, notes, categoryId, pricingMethod } = action.payload;
      const uniqueId = `custom_pizza_${Date.now()}_${Math.floor(Math.random() * 10000)}`;
      
      let price = 0;
      if (pricingMethod === 'max') {
        price = Math.max(...quarters.map(q => q.price));
      } else {
        price = quarters.reduce((sum, q) => sum + q.price, 0) / quarters.length;
      }
      
      const newItem: OrderItem = {
        id: uniqueId,
        menuItemId: 'custom_pizza',
        name: `Pizza Personnalisée (${quarters.map(q => q.name).join(', ')})`,
        size: size,
        price: price,
        quantity: 1,
        addons: [],
        notes: notes,
        categoryId: categoryId,
        compositeType: 'pizza_quarters',
        quarters: quarters
      };
      
      const updatedItems = [...state.items, newItem];
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }
      
    case 'UPDATE_ITEM': {
      const { itemId, updates } = action.payload;
      const updatedItems = state.items.map(item => 
        item.id === itemId ? { ...item, ...updates } : item
      );
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }
      
    case 'INCREMENT_QUANTITY': {
      const { itemId } = action.payload;
      const updatedItems = state.items.map(item => 
        item.id === itemId ? { ...item, quantity: item.quantity + 1 } : item
      );
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }
      
    case 'DECREMENT_QUANTITY': {
      const { itemId } = action.payload;
      const targetItem = state.items.find(item => item.id === itemId);
      
      if (!targetItem || targetItem.quantity <= 1) {
        const updatedItems = state.items.filter(item => item.id !== itemId);
        return {
          ...state,
          items: updatedItems,
          total: calculateTotal(updatedItems)
        };
      }
      
      const updatedItems = state.items.map(item => 
        item.id === itemId ? { ...item, quantity: item.quantity - 1 } : item
      );
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }
      
    case 'REMOVE_ITEM': {
      const { itemId } = action.payload;
      const updatedItems = state.items.filter(item => item.id !== itemId);
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }
      
    case 'SET_TABLE': {
      return {
        ...state,
        tableId: action.payload.tableId
      };
    }
      
    case 'SET_NOTES': {
      return {
        ...state,
        notes: action.payload.notes
      };
    }
      
    default:
      return state;
  }
};

// Main component with performance optimizations
const EnhancedMobileOrderingInterface: React.FC = React.memo(() => {
  // Hooks (following NewOrderingInterface pattern)
  const { isAuthenticated, user } = useAuth();
  const { categories, isLoading: menuLoading, error: menuError, isReady: menuReady } = useMenuV4();
  const { tables, isLoading: tablesLoading, error: tablesError, isReady: tablesReady } = useTableDB();
  const { createOrder } = useOrderV4();
  const { toast } = useToast();

  // State
  const [orderState, dispatch] = useReducer(orderReducer, initialOrderState);
  const [uiState, setUiState] = useState<UiState>(initialUiState);
  const [isPlacingOrder, setIsPlacingOrder] = useState(false);

  // Show table selection if no table is selected
  useEffect(() => {
    if (!orderState.tableId && tablesReady) {
      setUiState(prev => ({ ...prev, showTableSelection: true }));
    }
  }, [orderState.tableId, tablesReady]);

  // Set initial category when menu loads
  useEffect(() => {
    if (menuReady && categories && categories.length > 0 && !uiState.selectedCategory) {
      setUiState(prev => ({ ...prev, selectedCategory: categories[0].id }));
    }
  }, [menuReady, categories, uiState.selectedCategory]);

  // Helper functions
  const getCustomizationKey = useCallback((itemId: string, size: string) => `${itemId}-${size}`, []);
  const getItemNoteKey = useCallback((itemId: string, size: string) => `${itemId}-${size}-note`, []);

  // Handler functions
  const handleIncrement = useCallback((itemId: string) => {
    dispatch({ type: 'INCREMENT_QUANTITY', payload: { itemId } });
  }, []);

  const handleDecrement = useCallback((itemId: string) => {
    dispatch({ type: 'DECREMENT_QUANTITY', payload: { itemId } });
  }, []);

  const handleRemoveItem = useCallback((itemId: string) => {
    dispatch({ type: 'REMOVE_ITEM', payload: { itemId } });
  }, []);

  const handleAddItem = useCallback(async (item: MenuItem, size: string) => {
    // Handle custom pizza selection
    if (item.id === 'custom_pizza') {
      const category = categories?.find(cat => cat.id === uiState.selectedCategory && cat.isQuarterable);
      if (category) {
        setUiState(prev => ({
          ...prev,
          selectedItemSizes: { ...prev.selectedItemSizes, [item.id]: size },
          selectedItemForCustomization: `${item.id}-${size}`,
          showCustomization: true
        }));
        return;
      }
    }

    // Find category for this item
    let categoryId = uiState.selectedCategory;
    for (const category of categories || []) {
      const foundItem = category.items?.find((menuItem: MenuItem) => menuItem.id === item.id);
      if (foundItem) {
        categoryId = category.id;
        break;
      }
    }

    const customizationKey = getCustomizationKey(item.id, size);
    const selectedAddons = uiState.selectedAddons[customizationKey] || new Set();
    const itemNotes = uiState.itemNotes[getItemNoteKey(item.id, size)] || '';

    // For now, create empty addons array - will be populated by customization panel
    const validAddonObjects: OrderAddon[] = [];

    dispatch({
      type: 'ADD_ITEM',
      payload: {
        item,
        size,
        addons: validAddonObjects,
        notes: itemNotes,
        categoryId
      }
    });

    // Set visual feedback
    const signature = `${item.id}-${size}`;
    setUiState(prev => ({ ...prev, lastAddedItem: signature }));
    setTimeout(() => setUiState(prev => ({ ...prev, lastAddedItem: null })), 2000);

    // Check if item has supplements - then show customization
    const category = categories?.find(cat => cat.id === categoryId);
    const hasSupplements = category && category.supplements && category.supplements.length > 0;

    if (hasSupplements) {
      setUiState(prev => ({
        ...prev,
        selectedItemForCustomization: customizationKey,
        selectedItemSizes: { ...prev.selectedItemSizes, [item.id]: size },
        showCustomization: true
      }));
    }

    toast({
      title: "Article ajouté",
      description: `${item.name} (${size}) ajouté à la commande`,
    });
  }, [uiState.selectedCategory, uiState.selectedAddons, uiState.itemNotes, categories, getCustomizationKey, getItemNoteKey, dispatch, toast]);

  const handlePlaceOrder = useCallback(async () => {
    if (orderState.items.length === 0) {
      toast({
        title: "Commande vide",
        description: "Ajoutez des articles avant de passer la commande",
        variant: "destructive"
      });
      return;
    }

    if (!orderState.tableId) {
      toast({
        title: "Table non sélectionnée",
        description: "Sélectionnez une table pour continuer",
        variant: "destructive"
      });
      return;
    }

    setIsPlacingOrder(true);

    try {
      const newOrder: Omit<OrderDocument, '_id' | 'type' | 'schemaVersion' | 'createdAt' | 'updatedAt'> = {
        tableId: orderState.tableId,
        orderType: 'dine-in',
        status: 'pending',
        items: orderState.items.map(item => ({ ...item, notes: item.notes || '' })),
        total: orderState.total,
        notes: orderState.notes,
        paymentStatus: 'unpaid',
        createdBy: user?.name || (user as any)?.username || 'unknown',
        createdByName: user?.name || 'Personnel Inconnu'
      };

      await createOrder(newOrder);

      toast({
        title: "✅ Commande créée",
        description: `Commande pour la table ${tables?.find(t => t.id === orderState.tableId)?.name} créée avec succès`,
      });

      // Reset order
      dispatch({ type: 'INITIALIZE_ORDER' });
      setUiState(initialUiState);

    } catch (error) {
      console.error('Error creating order:', error);
      toast({
        title: "Erreur",
        description: "Impossible de créer la commande. Veuillez réessayer.",
        variant: "destructive"
      });
    } finally {
      setIsPlacingOrder(false);
    }
  }, [orderState, user, createOrder, tables, toast]);

  return (
    <div className="h-full max-h-screen flex flex-col bg-background overflow-hidden">
      {/* Enhanced Header */}
      <MobileHeader 
        orderState={orderState}
        uiState={uiState}
        setUiState={setUiState}
        tables={tables}
        dispatch={dispatch}
      />

      {/* Enhanced Category Navigation */}
      <CategoryNavigation 
        categories={categories}
        selectedCategory={uiState.selectedCategory}
        setUiState={setUiState}
        menuLoading={menuLoading}
      />

      {/* Main Content */}
      <div className="flex-1 overflow-hidden relative">
        <MenuItemsGrid
          categories={categories}
          selectedCategory={uiState.selectedCategory}
          searchQuery={uiState.searchQuery}
          lastAddedItem={uiState.lastAddedItem}
          onAddItem={handleAddItem}
          menuLoading={menuLoading}
        />

        {/* Enhanced Floating Cart Button */}
        {orderState.items.length > 0 && (
          <FloatingCartButton
            items={orderState.items}
            total={orderState.total}
            onClick={() => setUiState(prev => ({ ...prev, showCartSummary: true }))}
          />
        )}
      </div>

      {/* Cart Summary Modal */}
      <Dialog open={uiState.showCartSummary} onOpenChange={(open) => setUiState(prev => ({ ...prev, showCartSummary: open }))}>
        <DialogContent className="max-w-[95vw] w-[95vw] max-h-[90vh] h-auto flex flex-col p-0 sm:max-w-lg">
          <DialogHeader className="p-4 border-b">
            <DialogTitle className="text-lg font-semibold">Votre commande</DialogTitle>
          </DialogHeader>
          <CartSummary
            items={orderState.items}
            total={orderState.total}
            onIncrement={handleIncrement}
            onDecrement={handleDecrement}
            onRemove={handleRemoveItem}
            onEdit={(item) => {
              // Close cart modal and open customization for editing
              setUiState(prev => ({
                ...prev,
                showCartSummary: false,
                selectedItemForCustomization: `${item.menuItemId}-${item.size}`,
                selectedItemSizes: { ...prev.selectedItemSizes, [item.menuItemId]: item.size },
                showCustomization: true,
                editingItemId: item.id
              }));
            }}
            onPlaceOrder={handlePlaceOrder}
            isPlacingOrder={isPlacingOrder}
          />
        </DialogContent>
      </Dialog>

      {/* Customization Modal */}
      <Dialog open={uiState.showCustomization} onOpenChange={(open) => setUiState(prev => ({ ...prev, showCustomization: open }))}>
        <DialogContent className="max-w-[95vw] w-[95vw] max-h-[90vh] h-auto flex flex-col p-0 sm:max-w-lg">
          <DialogHeader className="flex-shrink-0 p-3 border-b">
            <DialogTitle className="text-base font-semibold">
              {uiState.editingItemId ? 'Modifier l\'article' : 'Personnaliser l\'article'}
            </DialogTitle>
          </DialogHeader>
          <div className="flex-1 min-h-0 overflow-hidden">
            <CustomizationPanel
            selectedItemForCustomization={uiState.selectedItemForCustomization}
            categories={categories}
            selectedItemSizes={uiState.selectedItemSizes}
            selectedAddons={uiState.selectedAddons}
            itemNotes={uiState.itemNotes}
            getCustomizationKey={getCustomizationKey}
            getItemNoteKey={getItemNoteKey}
            onClose={() => setUiState(prev => ({ ...prev, showCustomization: false, selectedItemForCustomization: null, editingItemId: null }))}
            onConfirm={(addons, notes) => {
              // Handle customization confirmation
              setUiState(prev => ({ ...prev, showCustomization: false, selectedItemForCustomization: null, editingItemId: null }));
            }}
            setUiState={setUiState}
            dispatch={dispatch}
            orderItems={orderState.items}
            toast={toast}
            uiState={uiState}
          />
          </div>
        </DialogContent>
      </Dialog>

      {/* Table Selection Dialog */}
      <Dialog open={uiState.showTableSelection} onOpenChange={(open) => !open && setUiState(prev => ({ ...prev, showTableSelection: false }))}>
        <DialogContent className="max-w-[90vw] w-[90vw] max-h-[80vh] overflow-y-auto sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="text-center">Sélectionner une table</DialogTitle>
          </DialogHeader>
          <div className="grid grid-cols-2 gap-3 p-4">
            {tables?.map((table) => (
              <Button
                key={table.id}
                variant="outline"
                onClick={() => {
                  dispatch({ type: 'SET_TABLE', payload: { tableId: table.id } });
                  setUiState(prev => ({ ...prev, showTableSelection: false }));
                }}
                className="h-16 flex flex-col items-center justify-center"
              >
                <div className="text-lg font-bold">{table.name}</div>
                <div className="text-xs text-muted-foreground">{table.seats} places</div>
              </Button>
            ))}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
});

// MobileHeader Component with performance optimization
interface MobileHeaderProps {
  orderState: OrderState;
  uiState: UiState;
  setUiState: React.Dispatch<React.SetStateAction<UiState>>;
  tables: any[] | undefined;
  dispatch: React.Dispatch<OrderAction>;
}

const MobileHeader: React.FC<MobileHeaderProps> = React.memo(({ orderState, uiState, setUiState, tables, dispatch }) => {
  const totalItems = orderState.items.reduce((sum, item) => sum + item.quantity, 0);
  const currentTable = tables?.find(t => t.id === orderState.tableId);

  return (
    <div className="flex-shrink-0 border-b bg-background">
      <div className="px-3 py-2 space-y-2">
        {/* Simple Header Row */}
        <div className="flex items-center justify-between">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setUiState(prev => ({ ...prev, showTableSelection: true }))}
            className="h-8 px-3 text-sm"
          >
            {currentTable ? `Table ${currentTable.name}` : 'Table'}
          </Button>
          {totalItems > 0 && (
            <div className="flex items-center gap-2 text-sm">
              <span>{totalItems}</span>
              <span className="font-medium">{orderState.total.toFixed(0)} DA</span>
            </div>
          )}
        </div>

        {/* Simple Search */}
        <div className="relative">
          <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Rechercher..."
            value={uiState.searchQuery}
            onChange={(e) => setUiState(prev => ({ ...prev, searchQuery: e.target.value }))}
            className="pl-8 pr-8 h-8 text-sm"
            aria-label="Rechercher des articles dans le menu"
          />
          {uiState.searchQuery && (
            <button
              onClick={() => setUiState(prev => ({ ...prev, searchQuery: '' }))}
              className="absolute right-2 top-1/2 transform -translate-y-1/2"
              aria-label="Effacer la recherche"
              type="button"
            >
              <X className="h-4 w-4 text-muted-foreground" />
            </button>
          )}
        </div>
      </div>
    </div>
  );
});

// Enhanced CategoryNavigation Component with better tablet UX
interface CategoryNavigationProps {
  categories: any[] | undefined;
  selectedCategory: string;
  setUiState: React.Dispatch<React.SetStateAction<UiState>>;
  menuLoading: boolean;
}

const CategoryNavigation: React.FC<CategoryNavigationProps> = React.memo(({ 
  categories, 
  selectedCategory, 
  setUiState, 
  menuLoading 
}) => {
  return (
    <div className="flex-shrink-0 border-b bg-background">
      <ScrollArea className="w-full">
        <div className="flex gap-1 px-3 py-2">
          {menuLoading ? (
            [...Array(4)].map((_, i) => (
              <div key={i} className="h-8 w-16 bg-muted rounded animate-pulse flex-shrink-0" />
            ))
          ) : (
            categories?.map((category) => {
              const isActive = selectedCategory === category.id;
              return (
                <Button
                  key={category.id}
                  variant={isActive ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setUiState(prev => ({ ...prev, selectedCategory: category.id }))}
                  className="flex-shrink-0 h-8 px-3 text-xs"
                  aria-label={`Sélectionner la catégorie ${category.name}`}
                  aria-pressed={isActive}
                >
                  <span className="mr-1">{category.emoji || '🍽️'}</span>
                  <span className="truncate">{category.name}</span>
                </Button>
              );
            })
          )}
        </div>
      </ScrollArea>
    </div>
  );
});

// MenuItemsGrid Component
interface MenuItemsGridProps {
  categories: any[] | undefined;
  selectedCategory: string;
  searchQuery: string;
  lastAddedItem: string | null;
  onAddItem: (item: MenuItem, size: string) => void;
  menuLoading: boolean;
}

const MenuItemsGrid: React.FC<MenuItemsGridProps> = React.memo(({
  categories,
  selectedCategory,
  searchQuery,
  lastAddedItem,
  onAddItem,
  menuLoading
}) => {
  // Filter items based on category and search, plus add synthetic Custom item for quarterable categories
  const filteredItems = useMemo(() => {
    const currentCategory = categories?.find(cat => cat.id === selectedCategory);
    if (!currentCategory?.items) return [];

    let items = [...currentCategory.items];

    // Add synthetic Custom item for quarterable categories
    if (currentCategory.isQuarterable) {
      const customItem: MenuItem = {
        id: 'custom_pizza',
        name: 'Custom',
        description: 'Créez votre pizza personnalisée',
        prices: currentCategory.sizes?.reduce((acc, size) => ({ ...acc, [size]: 0 }), {}) || { 'Standard': 0 },
        color: '#f97316' // Orange color for custom pizza
      };
      items = [customItem, ...items];
    }

    if (searchQuery.trim()) {
      items = items.filter((item: MenuItem) =>
        item.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    return items;
  }, [categories, selectedCategory, searchQuery]);

  if (menuLoading) {
    return (
      <div className="h-full overflow-hidden">
        <ScrollArea className="h-full">
          <div className="p-3 space-y-2">
            {[...Array(8)].map((_, i) => (
              <div key={i} className="h-12 bg-muted rounded animate-pulse" />
            ))}
          </div>
        </ScrollArea>
      </div>
    );
  }

  if (filteredItems.length === 0) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center p-6">
          <Search className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
          <p className="text-sm text-muted-foreground">
            {searchQuery ? 'Aucun article trouvé' : 'Aucun article dans cette catégorie'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full overflow-hidden">
      <ScrollArea className="h-full">
        <div className="p-3 pb-20 space-y-2">
          {filteredItems.map((item: MenuItem) => (
            <MenuItemCard
              key={item.id}
              item={item}
              lastAddedItem={lastAddedItem}
              onAddItem={onAddItem}
            />
          ))}
        </div>
      </ScrollArea>
    </div>
  );
});

// Enhanced MenuItemCard Component with better visual design
interface MenuItemCardProps {
  item: MenuItem;
  lastAddedItem: string | null;
  onAddItem: (item: MenuItem, size: string) => void;
}

const MenuItemCard: React.FC<MenuItemCardProps> = React.memo(({ item, lastAddedItem, onAddItem }) => {
  const handleSizeClick = useCallback((size: string) => {
    onAddItem(item, size);
  }, [item, onAddItem]);

  const hasMultipleSizes = Object.keys(item.prices).length > 1;
  const sizes = Object.entries(item.prices);

  return (
    <div className="bg-card border rounded-lg">
      <div className="p-3">
        <div className="flex items-center justify-between mb-2">
          <h3 className="font-medium text-sm leading-tight flex-1 pr-2">{item.name}</h3>
          {item.color && (
            <div
              className="w-3 h-3 rounded-full flex-shrink-0"
              style={{ backgroundColor: item.color }}
            />
          )}
        </div>

        {/* Size Selection */}
        {hasMultipleSizes ? (
          <div className="grid grid-cols-2 gap-2">
            {sizes.map(([size, price]) => {
              const isJustAdded = lastAddedItem === `${item.id}-${size}`;

              return (
                <Button
                  key={`${item.id}-${size}`}
                  variant="outline"
                  size="sm"
                  onClick={() => handleSizeClick(size)}
                  className={cn(
                    "h-10 flex flex-col items-center justify-center p-2 text-xs",
                    isJustAdded && "border-green-500 bg-green-50 text-green-700"
                  )}
                  aria-label={`Ajouter ${item.name} taille ${size === "default" ? "standard" : size} au panier pour ${price} DA`}
                >
                  <span className="font-medium leading-none">
                    {size === "default" ? "Standard" : size}
                  </span>
                  <span className="font-semibold text-foreground leading-none mt-0.5">
                    {price as number} DA
                  </span>
                </Button>
              );
            })}
          </div>
        ) : (
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleSizeClick(sizes[0][0])}
            className={cn(
              "w-full h-10 flex items-center justify-between px-3 text-xs",
              lastAddedItem === `${item.id}-${sizes[0][0]}` && "border-green-500 bg-green-50 text-green-700"
            )}
            aria-label={`Ajouter ${item.name} au panier pour ${sizes[0][1]} DA`}
          >
            <span className="font-medium">
              {sizes[0][0] === "default" ? "Standard" : sizes[0][0]}
            </span>
            <span className="font-semibold">
              {sizes[0][1] as number} DA
            </span>
          </Button>
        )}
      </div>
    </div>
  );
});

// Enhanced FloatingCartButton Component with better design
interface FloatingCartButtonProps {
  items: OrderItem[];
  total: number;
  onClick: () => void;
}

const FloatingCartButton: React.FC<FloatingCartButtonProps> = React.memo(({ items, total, onClick }) => {
  const totalQuantity = items.reduce((sum, item) => sum + item.quantity, 0);

  return (
    <div className="absolute bottom-0 left-0 right-0 p-3 bg-gradient-to-t from-background to-transparent pointer-events-none">
      <Button
        onClick={onClick}
        className="w-full h-10 text-sm font-semibold pointer-events-auto"
        aria-label={`Voir le panier avec ${totalQuantity} article${totalQuantity > 1 ? 's' : ''} pour un total de ${total.toFixed(0)} DA`}
      >
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center">
            <ShoppingCart className="h-4 w-4 mr-2" />
            <span>
              {totalQuantity} article{totalQuantity > 1 ? 's' : ''}
            </span>
          </div>
          <span className="font-semibold">
            {total.toFixed(0)} DA
          </span>
        </div>
      </Button>
    </div>
  );
});

// CartSummary Component
interface CartSummaryProps {
  items: OrderItem[];
  total: number;
  onIncrement: (itemId: string) => void;
  onDecrement: (itemId: string) => void;
  onRemove: (itemId: string) => void;
  onEdit: (item: OrderItem) => void;
  onPlaceOrder: () => void;
  isPlacingOrder: boolean;
}

const CartSummary: React.FC<CartSummaryProps> = ({
  items,
  total,
  onIncrement,
  onDecrement,
  onRemove,
  onEdit,
  onPlaceOrder,
  isPlacingOrder
}) => {
  const totalItemCount = useMemo(() => {
    return items.reduce((sum, item) => sum + item.quantity, 0);
  }, [items]);

  if (items.length === 0) {
    return (
      <div className="h-full flex flex-col items-center justify-center p-6 text-center">
        <div className="w-12 h-12 bg-muted rounded-full flex items-center justify-center mb-3">
          <ShoppingCart className="h-6 w-6 text-muted-foreground" />
        </div>
        <p className="text-muted-foreground text-sm">Votre panier est vide</p>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Items List */}
      <div className="flex-1 overflow-hidden">
        <ScrollArea className="h-full">
          <div className="p-4 space-y-2">
            {items.map((item) => (
              <div key={item.id} className="bg-card border rounded-lg p-3">
                <div className="flex justify-between items-start mb-2">
                  <div className="flex-1">
                    <h4 className="font-medium text-sm leading-tight">{item.name}</h4>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge variant="outline" className="text-xs h-5 px-2">
                        {item.size === 'default' ? 'Standard' : item.size}
                      </Badge>
                      <span className="text-xs text-muted-foreground">
                        {item.price} DA
                      </span>
                    </div>
                    {item.addons && item.addons.length > 0 && (
                      <p className="text-xs text-muted-foreground mt-1 bg-muted px-2 py-1 rounded">
                        + {item.addons.map(addon => addon.name).join(', ')}
                      </p>
                    )}
                    {item.quarters && item.quarters.length > 0 && (
                      <p className="text-xs text-muted-foreground mt-1 bg-orange-50 px-2 py-1 rounded border border-orange-200">
                        🍕 {item.quarters.map(q => q.name).join(' + ')}
                      </p>
                    )}
                    {item.notes && (
                      <p className="text-xs text-muted-foreground mt-1 italic bg-accent px-2 py-1 rounded">
                        Note: {item.notes}
                      </p>
                    )}
                  </div>
                  <div className="flex items-center gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onEdit(item)}
                      className="text-primary h-6 w-6 p-0"
                      title="Modifier l'article"
                    >
                      <Edit2 className="h-3 w-3" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onRemove(item.id)}
                      className="text-destructive h-6 w-6 p-0"
                      title="Supprimer l'article"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onDecrement(item.id)}
                      className="h-8 w-8 p-0"
                    >
                      <Minus className="h-3 w-3" />
                    </Button>
                    <span className="font-semibold text-sm min-w-[2rem] text-center">
                      {item.quantity}
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onIncrement(item.id)}
                      className="h-8 w-8 p-0"
                    >
                      <Plus className="h-3 w-3" />
                    </Button>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-sm">
                      {((item.price + item.addons.reduce((sum, addon) => sum + addon.price, 0)) * item.quantity).toFixed(0)} DA
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </ScrollArea>
      </div>

      {/* Footer with Total and Place Order */}
      <div className="flex-shrink-0 p-4 border-t bg-background space-y-3">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Total</span>
          <span className="text-lg font-semibold">{total.toFixed(0)} DA</span>
        </div>
        <Button
          onClick={onPlaceOrder}
          disabled={isPlacingOrder}
          className="w-full h-10 text-sm font-medium"
        >
          {isPlacingOrder ? (
            <>
              <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-current mr-2" />
              Création...
            </>
          ) : (
            <>
              <Check className="h-4 w-4 mr-2" />
              Passer la commande
            </>
          )}
        </Button>
      </div>
    </div>
  );
};

// CustomizationPanel Component
interface CustomizationPanelProps {
  selectedItemForCustomization: string | null;
  categories: any[] | undefined;
  selectedItemSizes: {[key: string]: string};
  selectedAddons: {[key: string]: Set<string>};
  itemNotes: {[key: string]: string};
  getCustomizationKey: (itemId: string, size: string) => string;
  getItemNoteKey: (itemId: string, size: string) => string;
  onClose: () => void;
  onConfirm: (addons: OrderAddon[], notes: string) => void;
  setUiState: React.Dispatch<React.SetStateAction<UiState>>;
  dispatch: React.Dispatch<OrderAction>;
  orderItems: OrderItem[];
  toast: any;
  uiState: UiState;
}

const CustomizationPanel: React.FC<CustomizationPanelProps> = ({
  selectedItemForCustomization,
  categories,
  selectedItemSizes,
  selectedAddons,
  itemNotes,
  getCustomizationKey,
  getItemNoteKey,
  onClose,
  setUiState,
  dispatch,
  orderItems,
  toast,
  uiState
}) => {
  if (!selectedItemForCustomization) return null;

  // Parse exactly like NewOrderingInterface does
  const parts = selectedItemForCustomization.split('-');
  const itemId = parts.slice(0, -1).join('-'); // Everything except last part
  const sizeFromKey = parts[parts.length - 1]; // Last part is size
  const selectedSize = selectedItemSizes[itemId] || sizeFromKey || 'default';

  // Find item and category - handle custom pizza case
  let item: MenuItem | null = null;
  let category: any = null;

  if (itemId === 'custom_pizza') {
    // For custom pizza, find the quarterable category
    category = categories?.find(cat => cat.id === uiState.selectedCategory && cat.isQuarterable);
    if (category) {
      item = {
        id: 'custom_pizza',
        name: 'Pizza Personnalisée',
        description: 'Créez votre pizza personnalisée',
        prices: category.sizes?.reduce((acc: any, size: string) => ({ ...acc, [size]: 0 }), {}) || { 'Standard': 0 },
        color: '#f97316'
      } as MenuItem;
    }
  } else {
    // Regular item lookup
    for (const cat of categories || []) {
      const foundItem = cat.items?.find((menuItem: MenuItem) => menuItem.id === itemId);
      if (foundItem) {
        item = foundItem;
        category = cat;
        break;
      }
    }
  }

  if (!item || !category) {
    return (
      <div className="h-full flex flex-col items-center justify-center p-6 text-center">
        <AlertCircle className="h-8 w-8 mx-auto mb-3 text-destructive" />
        <h3 className="font-medium text-sm mb-2">Article non trouvé</h3>
        <p className="text-xs text-muted-foreground mb-4">
          ItemId: {itemId} non trouvé dans le menu
        </p>
        <Button onClick={onClose} className="mt-4" size="sm">
          Fermer
        </Button>
      </div>
    );
  }

  const customizationKey = getCustomizationKey(itemId, selectedSize);
  const noteKey = getItemNoteKey(itemId, selectedSize);
  
  // If editing an item, restore its current state
  const editingItem = uiState.editingItemId ? orderItems.find(item => item.id === uiState.editingItemId) : null;
  
  const currentAddons = useMemo(() => {
    if (editingItem && editingItem.addons) {
      // Restore addons from the item being edited
      return new Set(editingItem.addons.map((addon: OrderAddon) => addon.id));
    }
    return selectedAddons[customizationKey] || new Set<string>();
  }, [editingItem, selectedAddons, customizationKey]);
  
  const currentNote = editingItem?.notes || itemNotes[noteKey] || '';
  
  // Restore state if editing an item
  useEffect(() => {
    if (editingItem) {
      // Restore addons
      if (editingItem.addons && editingItem.addons.length > 0) {
        const addonIds = new Set(editingItem.addons.map(addon => addon.id));
        setUiState(prev => ({
          ...prev,
          selectedAddons: {
            ...prev.selectedAddons,
            [customizationKey]: addonIds
          }
        }));
      }
      
      // Restore pizza quarters if it's a custom pizza
      if (editingItem.quarters && editingItem.quarters.length > 0) {
        const restoredQuarters: (PizzaQuarter | null)[] = [null, null, null, null];
        editingItem.quarters.forEach((quarter, index) => {
          if (index < 4) {
            restoredQuarters[index] = quarter;
          }
        });
        setUiState(prev => ({
          ...prev,
          pizzaQuarters: restoredQuarters,
          pizzaPartition: editingItem.quarters!.length <= 2 ? 'half' : 'quarter'
        }));
      }
      
      // Restore notes
      if (editingItem.notes) {
        setUiState(prev => ({
          ...prev,
          itemNotes: {
            ...prev.itemNotes,
            [noteKey]: editingItem.notes || ''
          }
        }));
      }
    }
  }, [editingItem, customizationKey, noteKey, setUiState]);

  const handleToggleAddon = (addonId: string) => {
    setUiState(prev => {
      const currentAddons = prev.selectedAddons[customizationKey] || new Set<string>();
      const newAddons = new Set(currentAddons);

      if (newAddons.has(addonId)) {
        newAddons.delete(addonId);
      } else {
        newAddons.add(addonId);
      }

      return {
        ...prev,
        selectedAddons: {
          ...prev.selectedAddons,
          [customizationKey]: newAddons
        }
      };
    });
  };

  const handleUpdateNote = (note: string) => {
    setUiState(prev => ({
      ...prev,
      itemNotes: {
        ...prev.itemNotes,
        [noteKey]: note
      }
    }));
  };

  const handleConfirm = () => {
    // Handle custom pizza confirmation
    if (itemId === 'custom_pizza') {
      const category = categories?.find(cat => cat.id === uiState.selectedCategory && cat.isQuarterable);
      if (category && uiState.pizzaQuarters.some(q => q !== null)) {
        const filledQuarters = uiState.pizzaQuarters.filter(q => q !== null) as PizzaQuarter[];
        const pricingMethod = category.quarterPricingMethod || 'max';
        
        if (uiState.editingItemId) {
          // Update existing custom pizza
          dispatch({
            type: 'UPDATE_ITEM',
            payload: {
              itemId: uiState.editingItemId,
              updates: {
                quarters: filledQuarters,
                notes: currentNote,
                name: `Pizza Personnalisée (${filledQuarters.map(q => q.name).join(', ')})`
              }
            }
          });
          
          toast({
            title: "✅ Pizza modifiée",
            description: `Pizza mise à jour avec ${filledQuarters.length} quart${filledQuarters.length > 1 ? 's' : ''}`,
          });
        } else {
          // Add new custom pizza
          dispatch({
            type: 'ADD_CUSTOM_PIZZA',
            payload: {
              quarters: filledQuarters,
              size: selectedSize,
              notes: currentNote,
              categoryId: category.id,
              pricingMethod
            }
          });
          
          toast({
            title: "✅ Pizza personnalisée ajoutée",
            description: `Pizza avec ${filledQuarters.length} quart${filledQuarters.length > 1 ? 's' : ''} personnalisé${filledQuarters.length > 1 ? 's' : ''}`,
          });
        }
        
        // Reset pizza quarters
        setUiState(prev => ({
          ...prev,
          pizzaQuarters: [null, null, null, null]
        }));
      }
      onClose();
      return;
    }

    // Handle regular item customization
    let targetItem: OrderItem | undefined;

    if (uiState.editingItemId) {
      // If editing, find the specific item by ID
      targetItem = orderItems.find(orderItem => orderItem.id === uiState.editingItemId);
    } else {
      // If adding new, find the most recent item
      targetItem = [...orderItems].reverse().find(orderItem =>
        orderItem.menuItemId === itemId && orderItem.size === selectedSize
      );
    }

    if (targetItem) {
      // Get supplements with proper names and prices
      const validAddonObjects: OrderAddon[] = [];
      if (category && category.supplements) {
        for (const addonId of Array.from(currentAddons)) {
          const supplement = category.supplements.find((s: any) => s.id === addonId);
          if (supplement) {
            // Get price from category supplement config
            const supplementPrice = category.supplementConfig?.globalPricing?.[selectedSize] || 0;
            validAddonObjects.push({
              id: supplement.id,
              name: supplement.name,
              price: supplementPrice
            });
          }
        }
      }

      // Update the item
      dispatch({
        type: 'UPDATE_ITEM',
        payload: {
          itemId: targetItem.id,
          updates: {
            addons: validAddonObjects,
            notes: currentNote
          }
        }
      });

      toast({
        title: uiState.editingItemId ? "✅ Article modifié" : "✅ Article personnalisé",
        description: `${validAddonObjects.length} supplément${validAddonObjects.length > 1 ? 's' : ''} ${uiState.editingItemId ? 'mis à jour' : 'ajouté'}${validAddonObjects.length > 1 ? 's' : ''}`,
      });
    }

    onClose();
  };

  return (
    <div className="h-full flex flex-col">
      {/* Compact Header */}
      <div className="flex-shrink-0 p-3 border-b bg-background">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <h3 className="text-sm font-semibold">{item.name}</h3>
            <div className="flex items-center gap-2 mt-1">
              <Badge variant="outline" className="text-xs">
                {selectedSize === 'default' ? 'Standard' : selectedSize}
              </Badge>
              <span className="text-xs text-muted-foreground">
                {item.prices[selectedSize]} DA
              </span>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-6 w-6 p-0"
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        <ScrollArea className="h-full">
          <div className="p-3 space-y-3 pb-4">
            {/* Custom Pizza Section (mobile-optimized) */}
            {itemId === 'custom_pizza' ? (
              <MobilePizzaCustomization
                category={category}
                selectedSize={selectedSize}
                pizzaPartition={uiState.pizzaPartition}
                pizzaQuarters={uiState.pizzaQuarters}
                onPartitionChange={(partition) => setUiState(prev => ({ ...prev, pizzaPartition: partition }))}
                onQuarterChange={(index, pizza) => {
                  const newQuarters = [...uiState.pizzaQuarters];
                  newQuarters[index] = pizza;
                  setUiState(prev => ({ ...prev, pizzaQuarters: newQuarters }));
                }}
              />
            ) : (
              // Regular Supplements Section
              <SupplementsSection
                categoryId={category.id}
                selectedSize={selectedSize}
                selectedAddons={selectedAddons[customizationKey] || new Set<string>()}
                categories={categories}
                onToggleAddon={handleToggleAddon}
              />
            )}

            {/* Notes Section */}
            <div className="space-y-2">
              <label className="text-xs font-medium">Notes spéciales</label>
              <Input
                placeholder="ex: sans oignons, bien cuit..."
                value={currentNote}
                onChange={(e) => handleUpdateNote(e.target.value)}
                className="h-8 text-xs"
              />
            </div>
          </div>
        </ScrollArea>
      </div>

      {/* Footer */}
      <div className="flex-shrink-0 p-3 border-t bg-background">
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={onClose}
            className="flex-1 h-9 text-sm"
          >
            Annuler
          </Button>
          <Button
            onClick={handleConfirm}
            className="flex-1 h-9 text-sm"
          >
            <Check className="h-3 w-3 mr-2" />
            Confirmer
          </Button>
        </div>
      </div>
    </div>
  );
};

// SupplementsSection Component
interface SupplementsSectionProps {
  categoryId: string;
  selectedSize: string;
  selectedAddons: Set<string>;
  categories: any[] | undefined;
  onToggleAddon: (addonId: string) => void;
}

const SupplementsSection: React.FC<SupplementsSectionProps> = ({
  categoryId,
  selectedSize,
  selectedAddons,
  categories,
  onToggleAddon
}) => {
  const { supplements, isLoading, error, isReady } = useSupplements(categoryId);

  // Find the current category to get pricing configuration
  const category = categories?.find(cat => cat.id === categoryId);
  const activeSupplements = supplements.filter(supplement => supplement.isActive !== false);


  if (isLoading) {
    return (
      <div>
        <h4 className="text-sm font-medium mb-2">Suppléments</h4>
        <div className="space-y-1">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="h-8 bg-muted rounded animate-pulse" />
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div>
        <h4 className="text-sm font-medium mb-2">Suppléments</h4>
        <div className="p-2 bg-destructive/10 text-destructive rounded text-xs">
          Erreur de chargement
        </div>
      </div>
    );
  }

  if (!isReady) {
    return (
      <div>
        <h4 className="text-sm font-medium mb-2">Suppléments</h4>
        <p className="text-xs text-muted-foreground">Chargement...</p>
      </div>
    );
  }

  if (activeSupplements.length === 0) {
    return (
      <div>
        <h4 className="text-sm font-medium mb-2">Suppléments</h4>
        <p className="text-xs text-muted-foreground">Aucun supplément disponible</p>
      </div>
    );
  }

  return (
    <div>
      <h4 className="text-xs font-medium mb-2">Suppléments ({activeSupplements.length})</h4>
      
      <div className="space-y-1">
        {activeSupplements.map((supplement) => {
          const price = category?.supplementConfig?.globalPricing?.[selectedSize] || 
                       category?.supplementConfig?.globalPricing?.['default'] || 
                       50;
          const isSelected = selectedAddons.has(supplement.id);

          return (
            <div
              key={supplement.id}
              onClick={() => onToggleAddon(supplement.id)}
              className={cn(
                "flex items-center justify-between p-2 rounded border cursor-pointer transition-all active:scale-[0.98]",
                isSelected
                  ? "border-primary bg-primary/10 shadow-sm"
                  : "border-border hover:bg-accent hover:border-accent-foreground/20"
              )}
            >
              <div className="flex items-center gap-2">
                <div className={cn(
                  "w-3 h-3 rounded border flex items-center justify-center transition-colors",
                  isSelected ? "border-primary bg-primary" : "border-muted-foreground"
                )}>
                  {isSelected && <Check className="h-2 w-2 text-primary-foreground" />}
                </div>
                <span className="font-medium text-xs">{supplement.name}</span>
              </div>
              <span className="font-semibold text-xs text-green-600">+{price} DA</span>
            </div>
          );
        })}
      </div>
    </div>
  );
};

// MobilePizzaCustomization Component - Mobile-optimized pizza builder
interface MobilePizzaCustomizationProps {
  category: any;
  selectedSize: string;
  pizzaPartition: 'half' | 'quarter';
  pizzaQuarters: (PizzaQuarter | null)[];
  onPartitionChange: (partition: 'half' | 'quarter') => void;
  onQuarterChange: (index: number, pizza: PizzaQuarter | null) => void;
}

const MobilePizzaCustomization: React.FC<MobilePizzaCustomizationProps> = ({
  category,
  selectedSize,
  pizzaPartition,
  pizzaQuarters,
  onPartitionChange,
  onQuarterChange
}) => {
  const pizzaItems = category?.items || [];
  const filledQuartersCount = pizzaQuarters.filter(q => q !== null).length;

  if (pizzaItems.length === 0) {
    return (
      <div className="space-y-3">
        <h4 className="text-sm font-medium flex items-center gap-2">
          <Pizza className="h-4 w-4 text-orange-500" />
          Pizza Personnalisée
        </h4>
        <div className="p-3 bg-muted/50 rounded-lg text-center">
          <p className="text-xs text-muted-foreground">Aucune pizza disponible</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <h4 className="text-sm font-medium flex items-center gap-2">
          <Pizza className="h-4 w-4 text-orange-500" />
          Pizza Personnalisée
        </h4>
        <Badge variant="outline" className="text-xs">
          {filledQuartersCount}/4 quarts
        </Badge>
      </div>

      {/* Partition Type Selection - Mobile optimized */}
      <div className="grid grid-cols-2 gap-2">
        <Button
          variant={pizzaPartition === 'half' ? 'default' : 'outline'}
          size="sm"
          onClick={() => onPartitionChange('half')}
          className="h-8 text-xs"
        >
          2 Moitiés
        </Button>
        <Button
          variant={pizzaPartition === 'quarter' ? 'default' : 'outline'}
          size="sm"
          onClick={() => onPartitionChange('quarter')}
          className="h-8 text-xs"
        >
          4 Quarts
        </Button>
      </div>

      {/* Pizza Quarter Selection - Simplified for mobile */}
      <div className="space-y-2">
        {pizzaPartition === 'quarter' ? (
          <div className="grid grid-cols-1 gap-2">
            {['Quart 1', 'Quart 2', 'Quart 3', 'Quart 4'].map((label, index) => (
              <div key={index} className="flex items-center gap-2">
                <span className="text-xs font-medium w-12 flex-shrink-0">{label}:</span>
                <Select
                  value={pizzaQuarters[index]?.menuItemId || 'none'}
                  onValueChange={(value) => {
                    if (value === 'none') {
                      onQuarterChange(index, null);
                    } else {
                      const selectedPizza = pizzaItems.find((p: MenuItem) => p.id === value);
                      if (selectedPizza) {
                        onQuarterChange(index, {
                          menuItemId: selectedPizza.id,
                          name: selectedPizza.name,
                          price: selectedPizza.prices[selectedSize] || Object.values(selectedPizza.prices)[0] || 0,
                          size: selectedSize
                        });
                      }
                    }
                  }}
                >
                  <SelectTrigger className="h-8 text-xs flex-1">
                    <SelectValue placeholder="Choisir pizza" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none" className="text-xs">Aucune</SelectItem>
                    {pizzaItems.map((pizza: MenuItem) => (
                      <SelectItem key={pizza.id} value={pizza.id} className="text-xs">
                        {pizza.name} - {pizza.prices[selectedSize] || Object.values(pizza.prices)[0]} DA
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-1 gap-2">
            {['Moitié Gauche', 'Moitié Droite'].map((label, index) => (
              <div key={index} className="flex items-center gap-2">
                <span className="text-xs font-medium w-20 flex-shrink-0">{label}:</span>
                <Select
                  value={pizzaQuarters[index]?.menuItemId || 'none'}
                  onValueChange={(value) => {
                    if (value === 'none') {
                      onQuarterChange(index, null);
                    } else {
                      const selectedPizza = pizzaItems.find((p: MenuItem) => p.id === value);
                      if (selectedPizza) {
                        onQuarterChange(index, {
                          menuItemId: selectedPizza.id,
                          name: selectedPizza.name,
                          price: selectedPizza.prices[selectedSize] || Object.values(selectedPizza.prices)[0] || 0,
                          size: selectedSize
                        });
                      }
                    }
                  }}
                >
                  <SelectTrigger className="h-8 text-xs flex-1">
                    <SelectValue placeholder="Choisir pizza" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none" className="text-xs">Aucune</SelectItem>
                    {pizzaItems.map((pizza: MenuItem) => (
                      <SelectItem key={pizza.id} value={pizza.id} className="text-xs">
                        {pizza.name} - {pizza.prices[selectedSize] || Object.values(pizza.prices)[0]} DA
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Selected pizzas summary */}
      {filledQuartersCount > 0 && (
        <div className="p-3 bg-muted/30 rounded-lg">
          <h5 className="text-xs font-medium mb-2">Composition:</h5>
          <div className="space-y-1">
            {pizzaQuarters.map((quarter, index) => {
              if (!quarter) return null;
              return (
                <div key={index} className="flex items-center justify-between text-xs">
                  <span className="text-muted-foreground">
                    {pizzaPartition === 'quarter' ? `Q${index + 1}` : index === 0 ? 'Gauche' : 'Droite'}:
                  </span>
                  <span className="font-medium">{quarter.name}</span>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

export default EnhancedMobileOrderingInterface;