"use client"

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { format, parseISO, isValid, startOfDay, isSameDay } from 'date-fns';
import { fr } from 'date-fns/locale';
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import {
  ChevronDownIcon,
  ChevronUpIcon,
  BanknoteIcon,
  CreditCardIcon,
  TruckIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  ClockIcon,
  UserIcon,
  ShoppingCartIcon,
  Loader2,
  RefreshCwIcon
} from 'lucide-react';
import { formatCurrency } from '@/lib/utils/currency';
import { cn } from '@/lib/utils';

// New event-based structure for finance history
export interface FinanceEvent {
  id: string;
  type: 'order_payment' | 'collection_pending' | 'collection_completed' | 'manual_in' | 'manual_out' | 'expense';
  timestamp: string;
  netAmount: number; // Amount that actually entered/left the drawer
  performedBy: string;
  description: string;
  
  // Event-specific data
  orderPayment?: {
    orderId: string;
    orderType: 'dine-in' | 'takeaway' | 'delivery';
    customer: string;
    tableId?: string;
    items: string;
    paymentMethod: 'cash' | 'card';
    collectionStatus: 'immediate' | 'not_collected' | 'collected';
  };
  
  collection?: {
    driverName: string;
    driverId: string;
    driverType: 'staff' | 'freelancer';
    orderCount: number;
    orders: {
      orderId: string;
      amount: number;
      customer: string;
    }[];
    grossAmount: number; // Total collected from customers
    driverFee: number;   // Fee paid to driver
    netAmount: number;   // Amount that goes to drawer (gross - fee)
    status: 'pending' | 'completed';
    expectedAmount?: number;
    actualAmount?: number;
    discrepancy?: number;
  };
  
  manualTransaction?: {
    reason: string;
    category: string;
    notes?: string;
  };
}

interface EventBasedFinanceHistoryProps {
  events: FinanceEvent[];
  isLoading?: boolean;
  onRefresh?: () => void;
  title?: string;
  maxHeight?: string;
}

interface DayGroup {
  date: string;
  events: FinanceEvent[];
  totalIn: number;
  totalOut: number;
  netAmount: number;
}

export default function EventBasedFinanceHistory({
  events,
  isLoading = false,
  onRefresh,
  title = "Historique Financier",
  maxHeight = "600px"
}: EventBasedFinanceHistoryProps) {
  const [expandedDays, setExpandedDays] = useState<Set<string>>(new Set());
  const [expandedHours, setExpandedHours] = useState<Set<string>>(new Set());

  // Group events by day and hour (following old convention)
  const groupedEvents = useMemo(() => {
    const grouped: Record<string, Record<string, FinanceEvent[]>> = {};

    events.forEach(event => {
      const eventDate = new Date(event.timestamp);
      const dateStr = format(eventDate, 'yyyy-MM-dd');
      const hour = eventDate.getHours();
      const hourKey = `${hour}`;

      if (!grouped[dateStr]) {
        grouped[dateStr] = {};
      }

      if (!grouped[dateStr][hourKey]) {
        grouped[dateStr][hourKey] = [];
      }

      grouped[dateStr][hourKey].push(event);
    });

    // Sort days (most recent first) and hours within each day
    return Object.entries(grouped)
      .sort(([dateA], [dateB]) => new Date(dateB).getTime() - new Date(dateA).getTime())
      .map(([date, hourGroups]) => ({
        date,
        hourGroups: Object.entries(hourGroups)
          .sort(([hourA], [hourB]) => parseInt(hourB) - parseInt(hourA))
          .map(([hour, events]) => ({
            hour: parseInt(hour),
            events: events.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
          }))
      }));
  }, [events]);

  const toggleDay = (date: string) => {
    const newExpanded = new Set(expandedDays);
    if (newExpanded.has(date)) {
      newExpanded.delete(date);
    } else {
      newExpanded.add(date);
    }
    setExpandedDays(newExpanded);
  };

  const toggleHour = (groupId: string) => {
    const newExpanded = new Set(expandedHours);
    if (newExpanded.has(groupId)) {
      newExpanded.delete(groupId);
    } else {
      newExpanded.add(groupId);
    }
    setExpandedHours(newExpanded);
  };

  // Calculate day totals
  const calculateDayTotal = (hourGroups: any[]) => {
    return hourGroups.reduce((dayTotal, { events }) =>
      dayTotal + events.reduce((hourTotal: number, event: FinanceEvent) => hourTotal + event.netAmount, 0), 0
    );
  };

  // Calculate hour totals
  const calculateHourSummary = (events: FinanceEvent[]) => {
    const total = events.reduce((sum, e) => sum + e.netAmount, 0);
    const inflow = events.filter(e => e.netAmount > 0).reduce((sum, e) => sum + e.netAmount, 0);
    const outflow = Math.abs(events.filter(e => e.netAmount < 0).reduce((sum, e) => sum + e.netAmount, 0));
    return { total, inflow, outflow, count: events.length };
  };

  // Format hour range
  const formatHourRange = (hour: number) => {
    const nextHour = (hour + 1) % 24;
    return `${hour.toString().padStart(2, '0')}:00 - ${nextHour.toString().padStart(2, '0')}:00`;
  };

  // Format date
  const formatEventDate = (dateStr: string) => {
    return format(parseISO(dateStr), 'EEEE d MMMM', { locale: fr });
  };

  // Get event icon based on type
  const getEventIcon = (event: FinanceEvent) => {
    switch (event.type) {
      case 'order_payment':
        return event.orderPayment?.paymentMethod === 'card' 
          ? <CreditCardIcon className="h-4 w-4" />
          : <BanknoteIcon className="h-4 w-4" />;
      case 'collection_pending':
        return <ClockIcon className="h-4 w-4 text-orange-500" />;
      case 'collection_completed':
        return <TruckIcon className="h-4 w-4 text-blue-500" />;
      case 'manual_in':
        return <ArrowUpIcon className="h-4 w-4 text-green-500" />;
      case 'manual_out':
        return <ArrowDownIcon className="h-4 w-4 text-red-500" />;
      case 'expense':
        return <ShoppingCartIcon className="h-4 w-4 text-red-500" />;
      default:
        return <BanknoteIcon className="h-4 w-4" />;
    }
  };

  // Get event type label
  const getEventTypeLabel = (event: FinanceEvent) => {
    switch (event.type) {
      case 'order_payment':
        return `Commande ${event.orderPayment?.orderType || ''}`;
      case 'collection_pending':
        return 'Collection En Attente';
      case 'collection_completed':
        return 'Collection Terminée';
      case 'manual_in':
        return 'Dépôt Manuel';
      case 'manual_out':
        return 'Retrait Manuel';
      case 'expense':
        return 'Dépense';
      default:
        return event.type;
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BanknoteIcon className="h-5 w-5" />
            {title}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="border rounded-lg overflow-hidden">
      {/* Header - Compact like old convention */}
      <div className="flex items-center justify-between p-1.5 border-b bg-muted/20">
        <div className="flex items-center gap-1">
          <BanknoteIcon className="h-3.5 w-3.5 text-primary" />
          <h3 className="text-sm font-semibold">{title}</h3>
        </div>
        {onRefresh && (
          <Button variant="ghost" size="sm" onClick={onRefresh} className="h-6 w-6 p-0">
            <RefreshCwIcon className="h-3 w-3" />
          </Button>
        )}
      </div>

      <ScrollArea style={{ height: maxHeight }}>
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-4 w-4 animate-spin" />
          </div>
        ) : groupedEvents.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground text-sm">
            Aucun événement financier trouvé
          </div>
        ) : (
          <div>
            {groupedEvents.map(({ date, hourGroups }) => {
              const isDayExpanded = expandedDays.has(date);
              const dayTotal = calculateDayTotal(hourGroups);
              const dayEventCount = hourGroups.reduce((sum, { events }) => sum + events.length, 0);

              return (
                <div key={date}>
                  {/* Day header - Blue like old convention */}
                  <button
                    className="w-full bg-blue-50 hover:bg-blue-100 p-1.5 border-b border-blue-200 text-left"
                    onClick={() => toggleDay(date)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-1">
                        <ChevronDownIcon className={cn("h-3.5 w-3.5", isDayExpanded && "rotate-180")} />
                        <span className="text-xs font-medium">📅 {formatEventDate(date)}</span>
                        <span className="text-xs text-muted-foreground">
                          {dayEventCount} événement{dayEventCount > 1 ? 's' : ''}
                        </span>
                      </div>
                      <span className={cn(
                        "text-xs font-medium",
                        dayTotal >= 0 ? 'text-green-600' : 'text-red-600'
                      )}>
                        {dayTotal >= 0 ? '+' : ''}{formatCurrency(dayTotal)}
                      </span>
                    </div>
                  </button>

                  {/* Hour groups - Orange like old convention */}
                  {isDayExpanded && (
                    <div>
                      {hourGroups.map(({ hour, events }) => {
                        const groupId = `${date}_${hour}`;
                        const isHourExpanded = expandedHours.has(groupId) ?? true;
                        const summary = calculateHourSummary(events);

                        return (
                          <div key={groupId}>
                            {/* Hour header */}
                            <button
                              className="w-full bg-orange-50 hover:bg-orange-100 px-2 py-1 border-b border-orange-200 text-left"
                              onClick={() => toggleHour(groupId)}
                            >
                              <div className="flex justify-between items-center">
                                <div className="flex items-center gap-1">
                                  <ChevronDownIcon className={cn("h-3 w-3", isHourExpanded && "rotate-180")} />
                                  <ClockIcon className="h-3 w-3" />
                                  <span className="text-xs">{formatHourRange(hour)}</span>
                                  <span className="text-xs text-muted-foreground">
                                    {summary.count} événement{summary.count > 1 ? 's' : ''}
                                  </span>
                                </div>
                                <div className="text-xs">
                                  <span className="font-medium">{formatCurrency(summary.total)}</span>
                                  <span className="ml-1 text-green-600">+{formatCurrency(summary.inflow)}</span>
                                  <span className="ml-1 text-red-600">-{formatCurrency(summary.outflow)}</span>
                                </div>
                              </div>
                            </button>

                            {/* Events list - Compact table format */}
                            {isHourExpanded && (
                              <div className="bg-white">
                                {events.map((event) => (
                                  <EventRow key={event.id} event={event} />
                                ))}
                              </div>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        )}
      </ScrollArea>
    </div>
  );
}

// Compact event row component following old table convention
interface EventRowProps {
  event: FinanceEvent;
}

function EventRow({ event }: EventRowProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  // Get clean, relevant info for each event type
  const getEventInfo = () => {
    switch (event.type) {
      case 'order_payment':
        const orderNum = event.orderPayment?.orderId.split('-').pop() || event.orderPayment?.orderId || '?';

        if (event.orderPayment?.collectionStatus === 'not_collected') {
          return {
            type: 'Commande (Non Collectée)',
            typeColor: 'bg-orange-50 text-orange-700 border-orange-200',
            id: `#${orderNum}`,
            mainInfo: event.orderPayment.customer,
            subInfo: event.orderPayment.orderType,
            amount: event.netAmount,
            amountStatus: 'pending' // Not collected yet
          };
        } else {
          return {
            type: 'Commande',
            typeColor: 'bg-green-50 text-green-700 border-green-200',
            id: `#${orderNum}`,
            mainInfo: event.orderPayment.customer,
            subInfo: event.orderPayment.tableId ? `Table ${event.orderPayment.tableId}` : event.orderPayment.orderType,
            amount: event.netAmount,
            amountStatus: 'completed'
          };
        }

      case 'collection_completed':
        return {
          type: 'Collection',
          typeColor: 'bg-blue-50 text-blue-700 border-blue-200',
          id: `${event.collection?.orderCount || 0} cmd${(event.collection?.orderCount || 0) > 1 ? 's' : ''}`,
          mainInfo: event.collection?.driverName || 'Livreur',
          subInfo: `${formatCurrency(event.collection?.grossAmount || 0)} - ${formatCurrency(event.collection?.driverFee || 0)} frais`,
          amount: event.netAmount,
          amountStatus: 'completed'
        };

      case 'manual_in':
        return {
          type: 'Dépôt',
          typeColor: 'bg-green-50 text-green-700 border-green-200',
          id: '-',
          mainInfo: event.performedBy,
          subInfo: event.manualTransaction?.reason || event.description,
          amount: event.netAmount,
          amountStatus: 'completed'
        };

      case 'manual_out':
        return {
          type: 'Retrait',
          typeColor: 'bg-red-50 text-red-700 border-red-200',
          id: '-',
          mainInfo: event.performedBy,
          subInfo: event.manualTransaction?.reason || event.description,
          amount: event.netAmount,
          amountStatus: 'completed'
        };

      default:
        return {
          type: event.type,
          typeColor: 'bg-gray-50 text-gray-700 border-gray-200',
          id: '-',
          mainInfo: event.performedBy,
          subInfo: event.description,
          amount: event.netAmount,
          amountStatus: 'completed'
        };
    }
  };

  const eventInfo = getEventInfo();
  const eventTime = format(parseISO(event.timestamp), 'HH:mm');

  return (
    <div className="border-b border-gray-100 last:border-b-0">
      {/* Clean, consistent row */}
      <div
        className="flex items-center py-2 px-3 hover:bg-muted/20 cursor-pointer text-sm border-b border-gray-100 last:border-b-0"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        {/* Event type */}
        <div className="w-[120px] flex-shrink-0">
          <Badge variant="outline" className={cn("text-xs px-2 py-1", eventInfo.typeColor)}>
            {eventInfo.type}
          </Badge>
        </div>

        {/* ID */}
        <div className="w-[60px] flex-shrink-0 font-mono font-bold text-blue-600">
          {eventInfo.id}
        </div>

        {/* Main info */}
        <div className="w-[120px] flex-shrink-0 truncate font-medium">
          {eventInfo.mainInfo}
        </div>

        {/* Sub info */}
        <div className="flex-1 min-w-0 truncate text-muted-foreground px-2">
          {eventInfo.subInfo}
        </div>

        {/* Time */}
        <div className="w-[50px] flex-shrink-0 text-muted-foreground text-xs">
          {eventTime}
        </div>

        {/* Amount */}
        <div className="w-[100px] flex-shrink-0 text-right font-medium">
          {eventInfo.amountStatus === 'pending' ? (
            <span className="text-orange-600">
              {formatCurrency(eventInfo.amount)} <span className="text-xs">(NC)</span>
            </span>
          ) : (
            <span className={cn(
              eventInfo.amount >= 0 ? "text-green-600" : "text-red-600"
            )}>
              {eventInfo.amount >= 0 ? '+' : ''}{formatCurrency(eventInfo.amount)}
            </span>
          )}
        </div>

        {/* Expand icon */}
        <div className="w-[30px] flex-shrink-0 flex justify-center">
          <ChevronDownIcon className={cn("h-4 w-4 text-muted-foreground", isExpanded && "rotate-180")} />
        </div>
      </div>

      {/* Expanded details */}
      {isExpanded && (
        <div className="px-4 py-2 bg-muted/10 border-t">
          <EventDetails event={event} />
        </div>
      )}
    </div>
  );
}

// Relevant details only
function EventDetails({ event }: { event: FinanceEvent }) {
  if (event.orderPayment) {
    return (
      <div className="bg-gray-50 p-3 rounded text-sm space-y-2">
        <div className="flex justify-between">
          <span className="text-muted-foreground">Articles:</span>
          <span className="font-medium">{event.orderPayment.items}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-muted-foreground">Paiement:</span>
          <Badge variant={event.orderPayment.paymentMethod === 'card' ? 'default' : 'secondary'} className="text-xs">
            {event.orderPayment.paymentMethod === 'card' ? 'Carte' : 'Espèces'}
          </Badge>
        </div>
        {event.orderPayment.collectionStatus === 'not_collected' && (
          <div className="bg-orange-100 p-2 rounded border border-orange-300 mt-2">
            <div className="text-orange-800 font-medium text-xs">⚠️ Argent non collecté - pas encore en caisse</div>
          </div>
        )}
      </div>
    );
  }

  if (event.collection) {
    return (
      <div className="bg-blue-50 p-3 rounded text-sm space-y-3">
        {/* Collection breakdown */}
        <div className="space-y-1">
          <div className="flex justify-between">
            <span>Collecté:</span>
            <span className="font-medium">{formatCurrency(event.collection.grossAmount)}</span>
          </div>
          <div className="flex justify-between">
            <span>Frais:</span>
            <span className="text-red-600">-{formatCurrency(event.collection.driverFee)}</span>
          </div>
          <div className="flex justify-between font-medium border-t pt-1">
            <span>Net:</span>
            <span className="text-green-600">+{formatCurrency(event.collection.netAmount)}</span>
          </div>
        </div>

        {/* Orders collected */}
        {event.collection.orders.length > 0 && (
          <div>
            <div className="font-medium mb-2">Commandes collectées:</div>
            <div className="space-y-1">
              {event.collection.orders.map((order, index) => (
                <div key={index} className="flex justify-between text-xs bg-white p-1 rounded">
                  <span className="font-mono text-blue-600">#{order.orderId.split('-').pop()}</span>
                  <span className="truncate mx-2 flex-1">{order.customer}</span>
                  <span>{formatCurrency(order.amount)}</span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  }

  if (event.manualTransaction) {
    return (
      <div className="bg-gray-50 p-3 rounded text-sm">
        <div className="flex justify-between">
          <span className="text-muted-foreground">Détails:</span>
          <span>{event.manualTransaction.reason}</span>
        </div>
        {event.manualTransaction.notes && (
          <div className="mt-2 pt-2 border-t">
            <div className="text-muted-foreground text-xs">Notes:</div>
            <div className="text-xs">{event.manualTransaction.notes}</div>
          </div>
        )}
      </div>
    );
  }

  return null;
}
